import asyncio
import ssl
import os
import logging
from collections import deque
from typing import Deque, Dict, Optional, cast, Tuple, List
from urllib.parse import urlparse

from aioquic.asyncio import connect, QuicConnectionProtocol
from aioquic.h3.connection import H3Connection
from aioquic.h3.events import DataReceived, H3Event, HeadersReceived, PushPromiseReceived  # Removed StreamReset
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent, StreamDataReceived, HandshakeCompleted, ConnectionTerminated
from aioquic.quic.logger import QuicLogger

# --- Configuration ---
TARGET_URL = "https://cloudflare-quic.com/"
# TARGET_URL = "https://quic.rocks:4433/"
# TARGET_URL = "https://www.google.com"
# TARGET_URL = "https://localhost:4433/" # For local testing

COOKIE_TO_SEND = "session_id=abc123xyz; user_preference=dark_mode"

# For TLS key logging (Wireshark decryption)
SSLKEYLOGFILE = os.environ.get("SSLKEYLOGFILE")
# For QLOG logging (detailed QUIC event logs)
QLOGDIR = os.environ.get("QLOGDIR")

# Configure logging for aioquic and our client
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s %(message)s",
)
logger = logging.getLogger("qpack_client")


# --- HTTP/3 Client Protocol ---

class HttpClientProtocol(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._h3_connection: H3Connection = H3Connection(self._quic)
        self._http_events: Dict[int, Deque[H3Event]] = {}
        self._http_request_done: Dict[int, asyncio.Future[None]] = {}

    def quic_event_received(self, event: QuicEvent) -> None:
        if isinstance(event, HandshakeCompleted):
            logger.info("QUIC handshake completed.")

        elif isinstance(event, ConnectionTerminated):
            logger.warning(f"QUIC Connection terminated: Error {event.error_code}, Reason: {event.reason_phrase}")
            for stream_id, future in self._http_request_done.items():
                if not future.done():
                    future.set_exception(
                        ConnectionError(f"Connection terminated: {event.error_code} - {event.reason_phrase}"))



    def _h3_event_received(self, event: H3Event) -> None:
        stream_id = getattr(event, "stream_id", None)

        if isinstance(event, HeadersReceived):
            logger.info(f"Stream {event.stream_id}: Headers received: {event.headers}")
            if event.stream_id not in self._http_events:
                self._http_events[event.stream_id] = deque()
            self._http_events[event.stream_id].append(event)

        elif isinstance(event, DataReceived):
            logger.debug(f"Stream {event.stream_id}: Data received ({len(event.data)} bytes)")
            if event.stream_id not in self._http_events:
                self._http_events[event.stream_id] = deque()
            self._http_events[event.stream_id].append(event)

        # Removed StreamReset handling here as it's a QUIC event, not an H3Event from H3Connection

        elif isinstance(event, PushPromiseReceived):
            logger.info(
                f"Stream {event.stream_id}: Push promise received for push ID {event.push_id} with headers {event.headers}")
            logger.info(f"Rejecting push {event.push_id} (associated with stream {event.stream_id})")
            self._h3_connection.cancel_push(push_id=event.push_id)  # Corrected method
            self.transmit()

        if stream_id is not None and getattr(event, "stream_ended", False):
            if stream_id in self._http_request_done and not self._http_request_done[stream_id].done():
                logger.info(f"Stream {stream_id} ended.")
                self._http_request_done[stream_id].set_result(None)

    async def get(self, url: str, headers: Optional[List[Tuple[bytes, bytes]]] = None) -> Tuple[
        List[Tuple[bytes, bytes]], bytes]:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Invalid URL provided for GET request")

        request_headers = [
            (b":method", b"GET"),
            (b":scheme", parsed_url.scheme.encode()),
            (b":authority", parsed_url.netloc.encode()),
            (b":path",
             (parsed_url.path or "/").encode() + (b"?" + parsed_url.query.encode() if parsed_url.query else b"")),
            (b"user-agent", b"aioquic-qpack-client/1.0"),
        ]
        if headers:
            request_headers.extend(headers)

        stream_id = self._h3_connection.get_next_available_stream_id()
        logger.info(f"Sending GET request on stream {stream_id} to {url} with headers: {request_headers}")

        self._h3_connection.send_headers(stream_id=stream_id, headers=request_headers)
        self._h3_connection.send_data(stream_id=stream_id, data=b"", end_stream=True)
        self.transmit()

        self._http_events[stream_id] = deque()
        self._http_request_done[stream_id] = asyncio.Future()

        await self._http_request_done[stream_id]

        response_headers: List[Tuple[bytes, bytes]] = []
        response_body_parts: List[bytes] = []

        while self._http_events.get(stream_id):  # Check if deque exists before popping
            event = self._http_events[stream_id].popleft()
            if isinstance(event, HeadersReceived):
                response_headers.extend(event.headers)
            elif isinstance(event, DataReceived):
                response_body_parts.append(event.data)

        if stream_id in self._http_events and not self._http_events[stream_id]:  # remove empty deque
            del self._http_events[stream_id]

        logger.info(f"Stream {stream_id}: Request completed.")
        return response_headers, b"".join(response_body_parts)

    def close(self):
        if self._quic and not self._quic.is_closed:
            logger.info("Closing QUIC connection.")
            self._quic.close()
            self.transmit()


async def run_client(host: str, port: int, url_to_fetch: str, cookie_header_value: str) -> None:
    logger.info(f"Attempting to connect to {host}:{port} for URL: {url_to_fetch}")

    secrets_log_file_handle = None
    if SSLKEYLOGFILE:
        logger.info(f"Logging QUIC TLS keys to {SSLKEYLOGFILE}")
        secrets_log_file_handle = open(SSLKEYLOGFILE, "a")

    quic_logger_instance: Optional[QuicLogger] = None
    if QLOGDIR:
        if not os.path.exists(QLOGDIR):
            try:
                os.makedirs(QLOGDIR)
                logger.info(f"Created QLOG directory: {QLOGDIR}")
            except OSError as e:
                logger.error(f"Could not create QLOGDIR {QLOGDIR}: {e}")
                QLOGDIR = None  # Disable qlogging if dir creation fails
        if QLOGDIR:  # Check again if QLOGDIR is still valid
            # Ensure unique qlog file per connection attempt or make it more robust
            qlog_path = os.path.join(QLOGDIR, f"client_{os.getpid()}_{id(asyncio.current_task())}.qlog")
            logger.info(f"Logging QUIC events (qlog) to {qlog_path}")
            quic_logger_instance = QuicLogger(path=qlog_path)

    config = QuicConfiguration(
        is_client=True,
        alpn_protocols=["h3"],
        secrets_log_file=secrets_log_file_handle,
        quic_logger=quic_logger_instance
    )

    # For HTTPS, `aioquic` uses system's CA bundle by default.
    # If your server uses a self-signed certificate:
    # 1. `config.load_verify_locations(cafile="path/to/your/ca.crt")`
    # 2. Or (INSECURE, for local testing ONLY): `config.verify_mode = ssl.CERT_NONE`
    # if "localhost" in host or "127.0.0.1" in host: # Example for local testing
    #     logger.warning("Localhost detected, disabling certificate verification for testing.")
    #     config.verify_mode = ssl.CERT_NONE

    try:
        async with connect(  # Removed quic_logger argument here
                host,
                port,
                configuration=config,
                create_protocol=HttpClientProtocol,
                wait_connected=True,
        ) as client:
            client = cast(HttpClientProtocol, client)

            custom_headers = [
                (b"cookie", cookie_header_value.encode("utf-8")),
                (b"x-custom-info", b"qpack-test-value"),
            ]

            logger.info(f"Making GET request to {url_to_fetch} with cookie: '{cookie_header_value}'")
            try:
                resp_headers, resp_body = await client.get(url_to_fetch, headers=custom_headers)

                logger.info("-" * 20 + " RESPONSE " + "-" * 20)
                logger.info("Response Headers:")
                for name, value in resp_headers:
                    logger.info(f"  {name.decode(errors='replace')}: {value.decode(errors='replace')}")
                try:
                    body_str = resp_body.decode("utf-8")
                    logger.info(
                        f"Response Body (first 500 chars):\n{body_str[:500]}{'...' if len(body_str) > 500 else ''}")
                except UnicodeDecodeError:
                    logger.info(f"Response Body (binary, first 100 bytes):\n{resp_body[:100].hex()}...")
                logger.info("-" * (40 + len(" RESPONSE ")))

                logger.info("Making a second GET request (to potentially benefit from QPACK dynamic table)")
                custom_headers_2 = [
                    (b"cookie", cookie_header_value.encode("utf-8")),
                    (b"x-custom-info", b"qpack-test-value-again"),
                ]
                resp_headers_2, resp_body_2 = await client.get(url_to_fetch, headers=custom_headers_2)
                logger.info("Second Response Headers:")
                for name, value in resp_headers_2:
                    logger.info(f"  {name.decode(errors='replace')}: {value.decode(errors='replace')}")
                logger.info("-" * (40 + len(" RESPONSE ")))

            except ConnectionError as e:
                logger.error(f"Connection error during HTTP request: {e}")
            except ConnectionResetError as e:
                logger.error(f"Connection reset during HTTP request: {e}")
            except asyncio.TimeoutError:
                logger.error("Request timed out.")
            except Exception as e:
                logger.error(f"An unexpected error occurred during HTTP request: {e}", exc_info=True)
            finally:
                logger.info("Request attempt finished, closing client protocol.")
                client.close()
                await asyncio.sleep(0.1)  # Give time for close frames

    except ConnectionRefusedError:
        logger.error(f"Connection refused by {host}:{port}.")
    except asyncio.TimeoutError:
        logger.error(f"Connection timed out to {host}:{port}.")
    except ssl.SSLCertVerificationError as e:
        logger.error(
            f"SSL Certificate Verification Error: {e}. If using a self-signed cert, configure verify_mode or load CA.")
    except Exception as e:
        logger.error(f"Failed to connect or run client: {e}", exc_info=True)
    finally:
        if secrets_log_file_handle:
            secrets_log_file_handle.close()


if __name__ == "__main__":
    parsed_target_url = urlparse(TARGET_URL)
    host = parsed_target_url.hostname
    port = parsed_target_url.port or (443 if parsed_target_url.scheme == 'https' else 80)

    if not host:
        logger.error(f"Could not parse hostname from URL: {TARGET_URL}")
        exit(1)

    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(
            run_client(
                host=host,
                port=port,
                url_to_fetch=TARGET_URL,
                cookie_header_value=COOKIE_TO_SEND,
            )
        )
    except KeyboardInterrupt:
        logger.info("Client interrupted by user.")
    finally:
        # Note: The qlog file (if enabled by QuicLogger(path=...)) is written continuously by aioquic.
        # No explicit dump is needed here for path-based QLOG.
        # The SSLKEYLOGFILE handle is closed in run_client's finally block.
        logger.info("Client shutdown complete.")