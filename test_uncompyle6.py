import uncompyle6

import os
import subprocess


def batch_decompile_pyc(pyc_directory, output_directory):
    # 确保输出目录存在
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    # 遍历目录中的所有文件
    for root, _, files in os.walk(pyc_directory):
        for file in files:
            if file.endswith('.pyc'):
                pyc_path = os.path.join(root, file)
                py_output_path = os.path.join(output_directory, file[:-4] + ".py")

                # 执行反编译命令
                try:
                    print(f"Decompiling {pyc_path}...")
                    subprocess.run(['uncompyle6', '-o', output_directory, pyc_path], check=True)
                except subprocess.CalledProcessError as e:
                    print(f"Failed to decompile {pyc_path}: {e}")


if __name__ == "__main__":
    # pyc_directory 为解包后含有 .pyc 文件的目录路径
    # output_directory 为反编译的 .py 文件保存路径
    pyc_directory = "D:\\ai\\AI人脸替换工具离线版(3)\\AI人脸替换工具V4.0启动包\\AI人脸替换工具离线版V4.5.exe_extracted\\"
    output_directory = "D:\\ai\\decompiled_output"

    batch_decompile_pyc(pyc_directory, output_directory)

