import argparse
import asyncio
import logging
import os
import pickle
import ssl
import time
from collections import deque
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast
from urllib.parse import urlparse, urlencode

import aioquic
import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket
from datetime import datetime

try:
    import uvloop
except ImportError:
    uvloop = None
import uuid


logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/" + aioquic.__version__


class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)

        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            headers: Optional[Dict] = None,
    ) -> None:
        if headers is None:
            headers = {}

        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class WebSocket:
    def __init__(
            self, http: HttpConnection, stream_id: int, transmit: Callable[[], None]
    ) -> None:
        """
        WebSocket对象的构造函数

        :param http: HTTP连接对象
        :param stream_id: WebSocket所在的流ID
        :param transmit: 一个可被调用的对象，调用时将使得HTTP连接对象
                         立即将缓存中的数据发送出去
        """
        self.http: HttpConnection = http  # HTTP连接对象
        self.queue: asyncio.Queue[str] = asyncio.Queue()  # 一个队列，用于保存
        # 从WebSocket中
        # 接收到的信息
        self.stream_id: int = stream_id  # WebSocket所在的流ID
        self.subprotocol: Optional[str] = None  # WebSocket的子协议
        self.transmit: Callable[[], None] = transmit  # 一个可被调用的对象，
        # 调用时将使得HTTP
        # 连接对象立即将
        # 缓存中的数据
        # 发送出去
        self.websocket: wsproto.Connection = wsproto.Connection(
            wsproto.ConnectionType.CLIENT
        )  # 一个WebSocket连接对象

    async def close(self, code: int = 1000, reason: str = "") -> None:
        """
        Perform the closing handshake.
        """
        data = self.websocket.send(
            wsproto.events.CloseConnection(code=code, reason=reason)
        )
        self.http.send_data(stream_id=self.stream_id, data=data, end_stream=True)
        self.transmit()

    async def recv(self) -> str:
        """
        Receive the next message.
        """
        return await self.queue.get()

    async def send(self, message: str) -> None:
        """
        Send a message.
        """
        assert isinstance(message, str)

        data = self.websocket.send(wsproto.events.TextMessage(data=message))
        self.http.send_data(stream_id=self.stream_id, data=data, end_stream=False)
        self.transmit()

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, HeadersReceived):
            for header, value in event.headers:
                if header == b"sec-websocket-protocol":
                    self.subprotocol = value.decode()
        elif isinstance(event, DataReceived):
            self.websocket.receive_data(event.data)

        for ws_event in self.websocket.events():
            self.websocket_event_received(ws_event)

    def websocket_event_received(self, event: wsproto.events.Event) -> None:
        if isinstance(event, wsproto.events.TextMessage):
            self.queue.put_nowait(event.data)


class HttpClient(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs) -> None:
        """
        HTTP客户端协议
        """
        super().__init__(*args, **kwargs)
        #: 一个字典，key是流ID，value是一个队列，保存该流ID
        #: 对应的推送事件
        self.pushes: Dict[int, Deque[H3Event]] = {}
        #: 一个HTTP连接对象
        self._http: Optional[HttpConnection] = None

        #: 一个字典，key是流ID，value是一个队列，保存该流ID
        #: 对应的请求响应事件
        self._request_events: Dict[int, Deque[H3Event]] = {}

        #: 一个字典，key是流ID，value是一个Future对象，对应
        #: 该流ID的请求响应事件
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}

        #: 一个字典，key是流ID，value是一个WebSocket对象
        self._websockets: Dict[int, WebSocket] = {}

        #: 选择使用H0还是H3
        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            self._http = H3Connection(self._quic)

    async def get(self, url: str, headers: Optional[Dict] = None) -> Deque[H3Event]:
        """
        Perform a GET request.
        """
        return await self._request(
            HttpRequest(method="GET", url=URL(url), headers=headers)
        )

    async def post(
            self, url: str, data: bytes, headers: Optional[Dict] = None
    ) -> Deque[H3Event]:
        """
        Perform a POST request.
        """
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def websocket(
            self, url: str, subprotocols: Optional[List[str]] = None
    ) -> WebSocket:
        """
        Open a WebSocket.
        """
        request = HttpRequest(method="CONNECT", url=URL(url))
        stream_id = self._quic.get_next_available_stream_id()
        websocket = WebSocket(
            http=self._http, stream_id=stream_id, transmit=self.transmit
        )

        self._websockets[stream_id] = websocket

        headers = [
            (b":method", b"CONNECT"),
            (b":scheme", b"https"),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b":protocol", b"websocket"),
            (b"user-agent", USER_AGENT.encode()),
            (b"sec-websocket-version", b"13"),
        ]
        if subprotocols:
            headers.append(
                (b"sec-websocket-protocol", ", ".join(subprotocols).encode())
            )
        self._http.send_headers(stream_id=stream_id, headers=headers)

        self.transmit()

        return websocket

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                # http
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))

            elif stream_id in self._websockets:
                # websocket
                websocket = self._websockets[stream_id]
                websocket.http_event_received(event)

            elif event.push_id in self.pushes:
                # push
                self.pushes[event.push_id].append(event)

        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        #  pass event to the HTTP layer
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        stream_id = self._quic.get_next_available_stream_id()
        _headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        # 控制单个请求cookie的重复次数,使得第一次和第二次保持相同
        # 处理请求头，支持字典或列表
        if isinstance(request.headers, dict):
            request_headers = [(k.encode(), v.encode()) for k, v in request.headers.items()]
        else:
            request_headers = [(k.encode(), v.encode()) for k, v in request.headers]
        _headers += request_headers

        print(f"_headers:{_headers}")
        self._http.send_headers(
            stream_id=stream_id,
            headers=_headers,
            end_stream=not request.content,
        )
        if request.content:
            self._http.send_data(
                stream_id=stream_id, data=request.content, end_stream=True
            )

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        return await asyncio.shield(waiter)


async def perform_http_request(
        client: HttpClient,
        url: str,
        data: Optional[str],
        include: bool,
        output_dir: Optional[str],
) -> None:
    # perform request
    start = time.time()
    if data is not None:
        data_bytes = data.encode()
        http_events = await client.post(
            url,
            data=data_bytes,
            headers={
                "content-length": str(len(data_bytes)),
                "content-type": "application/x-www-form-urlencoded",
            },
        )
        method = "POST"
    else:
        http_events = await client.get(url)
        method = "GET"
    elapsed = time.time() - start

    # print speed
    octets = 0
    for http_event in http_events:
        if isinstance(http_event, DataReceived):
            octets += len(http_event.data)
    logger.info(
        "Response received for %s %s : %d bytes in %.1f s (%.3f Mbps)"
        % (method, urlparse(url).path, octets, elapsed, octets * 8 / elapsed / 1000000)
    )

    # output response
    if output_dir is not None:
        output_path = os.path.join(
            output_dir, os.path.basename(urlparse(url).path) or "index.html"
        )
        with open(output_path, "wb") as output_file:
            write_response(
                http_events=http_events, include=include, output_file=output_file
            )


def process_http_pushes(
        client: HttpClient,
        include: bool,
        output_dir: Optional[str],
) -> None:
    for _, http_events in client.pushes.items():
        method = ""
        octets = 0
        path = ""
        for http_event in http_events:
            if isinstance(http_event, DataReceived):
                octets += len(http_event.data)
            elif isinstance(http_event, PushPromiseReceived):
                for header, value in http_event.headers:
                    if header == b":method":
                        method = value.decode()
                    elif header == b":path":
                        path = value.decode()
        logger.info("Push received for %s %s : %s bytes", method, path, octets)

        # output response
        if output_dir is not None:
            output_path = os.path.join(
                output_dir, os.path.basename(path) or "index.html"
            )
            with open(output_path, "wb") as output_file:
                write_response(
                    http_events=http_events, include=include, output_file=output_file
                )


def write_response(
        http_events: Deque[H3Event], output_file: BinaryIO, include: bool
) -> None:
    for http_event in http_events:
        if isinstance(http_event, HeadersReceived) and include:
            headers = b""
            for k, v in http_event.headers:
                headers += k + b": " + v + b"\r\n"
            if headers:
                output_file.write(headers + b"\r\n")
        elif isinstance(http_event, DataReceived):
            output_file.write(http_event.data)


def save_session_ticket(ticket: SessionTicket) -> None:
    """
    Callback which is invoked by the TLS engine when a new session ticket
    is received.
    """
    logger.info("New session ticket received")
    if args.session_ticket:
        with open(args.session_ticket, "wb") as fp:
            pickle.dump(ticket, fp)


async def main(configuration: QuicConfiguration):
    # base_url = "https://aliyun.hawks.top"   #
    base_url = "https://tencent.snakin.top"

    # 自行调整保证不要命中cache 可以用uuid的库自动生成
    incrementing_value = 0
    # 请求数量
    num_requests = 200

    # Create a connection to the server
    async with connect(
            # "aliyun.hawks.top",
            "tencent.snakin.top",
            443,
            configuration=configuration,
            create_protocol=HttpClient,
    ) as client:
        client = cast(HttpClient, client)

        # 等待QPACK动态表建立
        await asyncio.sleep(0.5)  # 500ms的初始延迟

        for _ in range(num_requests):

            incrementing_value += 1
            random_uuid = uuid.uuid4()
            # 去除UUID中的横线，得到等长的随机字符串
            random_string = str(random_uuid).replace('-', '')

            # query_params = {"text": random_string}
            # 方便一一对应查看
            query_params = {random_string:incrementing_value}
            url = f"{base_url}?{urlencode(query_params)}"
            print(url)

            num_cookie_headers = (_ // 2) + 1  # 每两次一个数量的cookie
            # ##  1. 测试cookie value
            # # cookie_value = "a" * 2980
            #
            # # cookie_value = "a" * 2979  # 第 45 次请求，Cookie头部数量：23
            # cookie_value = "a" * 2979  # 第 17 次请求，Cookie头部数量：9 开始：“<head><title>400 Request Header Or Cookie Too Large</title></head>”，
            # # 第 21 次请求，Cookie头部数量：11   Connection close sent
            #
            # # cookie_value = "a" * 2700  # 第 26 次请求，Cookie头部数量：13：  终端: “Stream 100 reset by peer (error code 1, final size 0)”
            # headers = [("Cookie", cookie_value)] * num_cookie_headers
            # print(f"第 { _+1 } 次请求，Cookie头部数量：{num_cookie_headers}")

            ##  2. 测试header  name 极限
            # myHeaderName = "1234567890"*10
            # headers = [(myHeaderName*incrementing_value,"myHeaderName")]

            ##  3. 测试header  value 极限
            # myHeaderName = "myHeaderName"
            # #  从至少10*200 个字符开始测试
            # headers = [(myHeaderName,"1234567890"*300+"12345678901234567890"*incrementing_value)]

            ##  4. 测试最大同一个字段 数量极限
            # myHeaderName = "myHeaderName"
            # #  从至少10*200 个字符开始测试
            # print(num_cookie_headers)
            # headers = [(myHeaderName,"1234567890"*300+"12345678901234567890"*100)]* num_cookie_headers  #  翻倍就有问题

            print(num_cookie_headers)
            headers = [('a' * 270, "e" * 2500)] * 3


            print(headers)
            print("incrementing_value:"+str(incrementing_value))
            response_events = await client.get(url, headers=headers)

            for event in response_events:
                if isinstance(event, HeadersReceived):
                    print(f"Received headers: {event.headers}")
                elif isinstance(event, DataReceived):
                    print(f"Received data: {event.data.decode()}")

            # 在请求之间添加小延迟，确保QPACK状态同步
            if _ < num_requests - 1:  # 最后一个请求不需要延迟
                await asyncio.sleep(0.1)  # 100ms的请求间隔

if __name__ == "__main__":
    defaults = QuicConfiguration(is_client=True)

    parser = argparse.ArgumentParser(description="HTTP/3 client")
    # parser.add_argument(
    #     "url", type=str, nargs="+", help="the URL to query (must be HTTPS)"
    # )
    parser.add_argument(
        "--ca-certs", type=str, help="load CA certificates from the specified file"
    )
    parser.add_argument(
        "--certificate",
        type=str,
        help="load the TLS certificate from the specified file",
    )
    parser.add_argument(
        "--cipher-suites",
        type=str,
        help=(
            "only advertise the given cipher suites, e.g. `AES_256_GCM_SHA384,"
            "CHACHA20_POLY1305_SHA256`"
        ),
    )
    parser.add_argument(
        "--congestion-control-algorithm",
        type=str,
        default="reno",
        help="use the specified congestion control algorithm",
    )
    parser.add_argument(
        "-d", "--data", type=str, help="send the specified data in a POST request"
    )
    parser.add_argument(
        "-i",
        "--include",
        action="store_true",
        help="include the HTTP response headers in the output",
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="do not validate server certificate",
    )
    parser.add_argument(
        "--legacy-http",
        action="store_true",
        help="use HTTP/0.9",
    )
    parser.add_argument(
        "--max-data",
        type=int,
        help="connection-wide flow control limit (default: %d)" % defaults.max_data,
    )
    parser.add_argument(
        "--max-stream-data",
        type=int,
        help="per-stream flow control limit (default: %d)" % defaults.max_stream_data,
    )
    parser.add_argument(
        "--negotiate-v2",
        action="store_true",
        help="start with QUIC v1 and try to negotiate QUIC v2",
    )

    parser.add_argument(
        "--output-dir",
        type=str,
        help="write downloaded files to this directory",
    )
    parser.add_argument(
        "--private-key",
        type=str,
        help="load the TLS private key from the specified file",
    )
    parser.add_argument(
        "-q",
        "--quic-log",
        type=str,
        help="log QUIC events to QLOG files in the specified directory",
    )
    parser.add_argument(
        "-l",
        "--secrets-log",
        type=str,
        help="log secrets to a file, for use with Wireshark",
    )
    parser.add_argument(
        "-s",
        "--session-ticket",
        type=str,
        help="read and write session ticket from the specified file",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="increase logging verbosity"
    )
    parser.add_argument(
        "--local-port",
        type=int,
        default=0,
        help="local port to bind for connections",
    )
    parser.add_argument(
        "--max-datagram-size",
        type=int,
        default=defaults.max_datagram_size,
        help="maximum datagram size to send, excluding UDP or IP overhead",
    )
    parser.add_argument(
        "--zero-rtt", action="store_true", help="try to send requests using 0-RTT"
    )

    args = parser.parse_args()

    logging.basicConfig(
        format="%(asctime)s %(levelname)s %(name)s %(message)s",
        level=logging.DEBUG if args.verbose else logging.INFO,
    )

    if args.output_dir is not None and not os.path.isdir(args.output_dir):
        raise Exception("%s is not a directory" % args.output_dir)

    # prepare configuration
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H0_ALPN if args.legacy_http else H3_ALPN,
        congestion_control_algorithm=args.congestion_control_algorithm,
        max_datagram_size=args.max_datagram_size,

    )

    configuration.verify_mode = False  # 出于测试目的，不验证服务器证书

    # 设置 secrets_log_file 属性，用来记录密钥日志，解密抓到的pcap
    # configuration.secrets_log_file = open("logs2.log", "w")
    log_file_name = datetime.now().strftime("%Y%m%d_%H%M%S.log")
    logs_dir = "./logs/"
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    log_file_path = os.path.join("./logs/", log_file_name)

    configuration.secrets_log_file = open(log_file_path, "w")

    if args.ca_certs:
        configuration.load_verify_locations(args.ca_certs)
    if args.cipher_suites:
        configuration.cipher_suites = [
            CipherSuite[s] for s in args.cipher_suites.split(",")
        ]
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
    if args.max_data:
        configuration.max_data = args.max_data
    if args.max_stream_data:
        configuration.max_stream_data = args.max_stream_data
    if args.negotiate_v2:
        configuration.original_version = QuicProtocolVersion.VERSION_1
        configuration.supported_versions = [
            QuicProtocolVersion.VERSION_2,
            QuicProtocolVersion.VERSION_1,
        ]
    if args.quic_log:
        configuration.quic_logger = QuicFileLogger(args.quic_log)
    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
    if args.session_ticket:
        try:
            with open(args.session_ticket, "rb") as fp:
                configuration.session_ticket = pickle.load(fp)
        except FileNotFoundError:
            pass

    # load SSL certificate and key
    if args.certificate is not None:
        configuration.load_cert_chain(args.certificate, args.private_key)

    if uvloop is not None:
        uvloop.install()
    asyncio.run(
        main(
            configuration=configuration,

        )
    )

