import asyncio
import logging
from urllib.parse import urlparse, urlencode

from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h3.connection import H3Connection, ErrorCode
from aioquic.h3.events import (
    DataReceived,
    HeadersReceived,
    PushPromiseReceived,
    H3Event,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("client")


class HttpClient(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self._http = H3Connection(self._quic)
        self._events = {}
        self._counter = 0  # 用于生成动态的 text 参数

    def quic_event_received(self, event: QuicEvent) -> None:
        # 将事件传递给 HTTP/3 层处理
        for http_event in self._http.handle_event(event):
            self.handle_event(http_event)

    def handle_event(self, event: H3Event):
        if isinstance(event, HeadersReceived):
            stream_id = event.stream_id
            headers = event.headers
            logger.info(f"收到 Headers (Stream ID: {stream_id}): {headers}")
        elif isinstance(event, DataReceived):
            stream_id = event.stream_id
            data = event.data
            logger.info(f"收到数据 (Stream ID: {stream_id}): {data.decode(errors='ignore')}")
            if event.stream_ended:
                logger.info(f"流 {stream_id} 已结束")
                self._events[stream_id].set()
        elif isinstance(event, PushPromiseReceived):
            logger.info(f"收到 Push Promise: {event}")
        else:
            logger.info(f"收到事件: {event}")

    async def send_requests(self, url, num_requests):
        # 多次发送请求
        tasks = [self._send_request(url) for _ in range(num_requests)]
        await asyncio.gather(*tasks)

    async def _send_request(self, url: str):
        self._counter += 1  # 动态增加计数器，便于分辨转发后对应包
        parsed_url = urlparse(url)

        if parsed_url.scheme != "https":
            raise ValueError("仅支持 HTTPS 协议的 URL。")

        # 添加动态参数 ?text={一个不断增加的正整数}
        query_params = {'textForIdentifyNumber': str(self._counter)}
        full_path = parsed_url.path or "/"
        full_path += '?' + urlencode(query_params)
        print(full_path)
        headers = [
            (b":method", b"GET"),
            (b":authority", parsed_url.netloc.encode()),
            (b":scheme", b"https"),
            (b":path", full_path.encode()),
            (b"cookie", b"user-session=12345aaaaa12345aaaaa12345aaaaa12345aaaaa12345aaaaa12345aaaaa12345aaaaa12345aaaaa"*10),  # 添加自定义 cookie
            (b"user-agent", b"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"),  # 自定义 User-Agent
        ]

        # 创建新的流
        stream_id = self._quic.get_next_available_stream_id()
        self._events[stream_id] = asyncio.Event()

        # 发送 Headers
        self._http.send_headers(
            stream_id=stream_id, headers=headers, end_stream=True
        )
        self.transmit()
        await self._events[stream_id].wait()
        logger.info(f"请求 {url} 的 Stream ID {stream_id} 已完成")

    async def close(self):
        self._quic.close(error_code=ErrorCode.H3_NO_ERROR, reason_phrase="")
        await self.wait_closed()


async def main():
    # 配置 QUIC
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=["h3"],
    )
    configuration.verify_mode = False  # 出于测试目的，不验证服务器证书

    # 设置 secrets_log_file 属性，用来记录密钥日志，解密抓到的pcap
    configuration.secrets_log_file = open("secrets2.log", "w")

    # 目标 URL
    # target_url = "https://aliyun.hawks.top"
    # target_url = "https://baidu.snakin.top"
    target_url = "https://fastly.cdn.linziyu.me"

    parsed_url = urlparse(target_url)
    host = parsed_url.hostname
    port = parsed_url.port or 443

    async with connect(
            host,
            port,
            configuration=configuration,
            create_protocol=HttpClient,
    ) as client:
        client = client  # 类型提示
        await client.send_requests(target_url, num_requests=5)
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())