('D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\PYZ-00.pyz',
 [('PIL',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'D:\\Work__\\Python_Version\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\Work__\\Python_Version\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Work__\\Python_Version\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Work__\\Python_Version\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Work__\\Python_Version\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Work__\\Python_Version\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Work__\\Python_Version\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Work__\\Python_Version\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Work__\\Python_Version\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Work__\\Python_Version\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Work__\\Python_Version\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Work__\\Python_Version\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Work__\\Python_Version\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Work__\\Python_Version\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\Work__\\Python_Version\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Work__\\Python_Version\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Work__\\Python_Version\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Work__\\Python_Version\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Work__\\Python_Version\\Lib\\calendar.py', 'PYMODULE'),
  ('cffi',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\Work__\\Python_Version\\Lib\\cgi.py', 'PYMODULE'),
  ('cmd', 'D:\\Work__\\Python_Version\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Work__\\Python_Version\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Work__\\Python_Version\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\Work__\\Python_Version\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Work__\\Python_Version\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Work__\\Python_Version\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Work__\\Python_Version\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Work__\\Python_Version\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Work__\\Python_Version\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Work__\\Python_Version\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Work__\\Python_Version\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\Work__\\Python_Version\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Work__\\Python_Version\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Work__\\Python_Version\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Work__\\Python_Version\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Work__\\Python_Version\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Work__\\Python_Version\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Work__\\Python_Version\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Work__\\Python_Version\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Work__\\Python_Version\\Lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Work__\\Python_Version\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'D:\\Work__\\Python_Version\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\Work__\\Python_Version\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Work__\\Python_Version\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Work__\\Python_Version\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Work__\\Python_Version\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Work__\\Python_Version\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Work__\\Python_Version\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Work__\\Python_Version\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Work__\\Python_Version\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Work__\\Python_Version\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Work__\\Python_Version\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Work__\\Python_Version\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Work__\\Python_Version\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Work__\\Python_Version\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Work__\\Python_Version\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Work__\\Python_Version\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Work__\\Python_Version\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Work__\\Python_Version\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Work__\\Python_Version\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Work__\\Python_Version\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Work__\\Python_Version\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fileinput', 'D:\\Work__\\Python_Version\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Work__\\Python_Version\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Work__\\Python_Version\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Work__\\Python_Version\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Work__\\Python_Version\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Work__\\Python_Version\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Work__\\Python_Version\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Work__\\Python_Version\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Work__\\Python_Version\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Work__\\Python_Version\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Work__\\Python_Version\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Work__\\Python_Version\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Work__\\Python_Version\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Work__\\Python_Version\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Work__\\Python_Version\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Work__\\Python_Version\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Work__\\Python_Version\\Lib\\http\\server.py',
   'PYMODULE'),
  ('imp', 'D:\\Work__\\Python_Version\\Lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Work__\\Python_Version\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Work__\\Python_Version\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Work__\\Python_Version\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Work__\\Python_Version\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Work__\\Python_Version\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Work__\\Python_Version\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Work__\\Python_Version\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Work__\\Python_Version\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Work__\\Python_Version\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Work__\\Python_Version\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Work__\\Python_Version\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Work__\\Python_Version\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Work__\\Python_Version\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Work__\\Python_Version\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Work__\\Python_Version\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Work__\\Python_Version\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Work__\\Python_Version\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Work__\\Python_Version\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Work__\\Python_Version\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Work__\\Python_Version\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\Work__\\Python_Version\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\Work__\\Python_Version\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Work__\\Python_Version\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Work__\\Python_Version\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Work__\\Python_Version\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Work__\\Python_Version\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('queue', 'D:\\Work__\\Python_Version\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Work__\\Python_Version\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Work__\\Python_Version\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Work__\\Python_Version\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Work__\\Python_Version\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Work__\\Python_Version\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Work__\\Python_Version\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Work__\\Python_Version\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Work__\\Python_Version\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Work__\\Python_Version\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Work__\\Python_Version\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\Work__\\Python_Version\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Work__\\Python_Version\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\Work__\\Python_Version\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Work__\\Python_Version\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Work__\\Python_Version\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Work__\\Python_Version\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Work__\\Python_Version\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Work__\\Python_Version\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Work__\\Python_Version\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Work__\\Python_Version\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Work__\\Python_Version\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Work__\\Python_Version\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Work__\\Python_Version\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\Work__\\Python_Version\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Work__\\Python_Version\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Work__\\Python_Version\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Work__\\Python_Version\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Work__\\Python_Version\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Work__\\Python_Version\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Work__\\Python_Version\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Work__\\Python_Version\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Work__\\Python_Version\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Work__\\Python_Version\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Work__\\Python_Version\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\Work__\\Python_Version\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\Work__\\Python_Version\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\Work__\\Python_Version\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Work__\\Python_Version\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Work__\\Python_Version\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Work__\\Python_Version\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Work__\\Python_Version\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Work__\\Python_Version\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Work__\\Python_Version\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Work__\\Python_Version\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Work__\\Python_Version\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Work__\\Python_Version\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Work__\\Python_Version\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Work__\\Python_Version\\Lib\\zipimport.py', 'PYMODULE')])
