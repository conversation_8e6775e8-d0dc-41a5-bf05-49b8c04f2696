('D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\MangaPDFConverterV2.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('manga_converter_gui',
   'D:\\Work__\\Pycharm_Work\\http3_python\\manga_converter_gui.py',
   'PYSOURCE'),
  ('python311.dll', 'D:\\Work__\\Python_Version\\python311.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-d64049c6e3865410a7dda6a7e9f0c575.dll',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy.libs\\msvcp140-d64049c6e3865410a7dda6a7e9f0c575.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Work__\\Python_Version\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Work__\\Python_Version\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Work__\\Python_Version\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Work__\\Python_Version\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Work__\\Python_Version\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Work__\\Python_Version\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Work__\\Python_Version\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Work__\\Python_Version\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Work__\\Python_Version\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Work__\\Python_Version\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Work__\\Python_Version\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Work__\\Python_Version\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll', 'D:\\Work__\\Python_Version\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Work__\\Python_Version\\DLLs\\libffi-8.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\Work__\\Python_Version\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\Work__\\Python_Version\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl\\tcl8.6\\auto.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\clock.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\ascii.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\big5.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cns11643.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1250.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1251.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1252.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1253.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1254.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1255.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1256.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1257.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp1258.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp437.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp737.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp775.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp850.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp852.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp855.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp857.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp860.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp861.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp862.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp863.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp864.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp865.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp866.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp869.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp874.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp932.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp936.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp949.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\cp950.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\dingbats.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\gb12345.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\gb1988.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\gb2312.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso2022.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\jis0201.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\jis0208.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\jis0212.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macGreek.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macIceland.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macJapan.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macRoman.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macRomania.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macThai.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\symbol.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tcl8.6\\encoding\\tis-620.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tcl8.6\\history.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\http1.0\\http.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\init.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\af.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\af_za.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ar.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ar_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\bg.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\bn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\bn_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ca.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\cs.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\da.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\de.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\de_at.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\de_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\el.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_au.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_bw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_ca.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_hk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_ie.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_nz.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_ph.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_sg.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_za.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\en_zw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\eo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_ar.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_bo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_cl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_co.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_cr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_do.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_ec.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_gt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_hn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_mx.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_ni.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_pa.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_pe.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_pr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_py.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_sv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_uy.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\es_ve.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\et.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\eu.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\eu_es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fa.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fa_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fi.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fr_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ga.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\gl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\gl_es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\gv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\he.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\hi.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\hi_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\hr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\hu.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\id.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\id_id.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\is.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\it.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\it_ch.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ja.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\kl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ko.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\kok.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\kok_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\kw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\lt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\lv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\mk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\mr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\mr_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ms.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ms_my.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\mt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\nb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\nl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\nl_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\nn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\pl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\pt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\pt_br.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ro.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ru.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sh.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sq.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\sw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ta.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\ta_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\te.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\te_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\th.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\tr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\uk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\vi.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\zh.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\package.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\parray.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\safe.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\tclIndex',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tcl8.6\\tm.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Adak',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Atka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Belem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Belize',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Boise',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Creston',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Denver',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Havana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Lima',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Managua',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Merida',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\New_York',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Nome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Panama',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Recife',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Regina',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Thule',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\North',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\South',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\West',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\CET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\CST6CDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Cuba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\EET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\EST',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\EST5EDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Egypt',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Eire',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\London',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\GB',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\GB-Eire',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\GMT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\GMT+0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\GMT-0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\GMT0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Greenwich',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\HST',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Hongkong',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Iceland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Iran',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Israel',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Jamaica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Japan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Kwajalein',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Libya',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\MET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\MST',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\MST7MDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\NZ',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Navajo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\PRC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\PST8PDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Poland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Portugal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\ROC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\ROK',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Singapore',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Turkey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\UCT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Central',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\UTC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Universal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\W-SU',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\WET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tcl8.6\\tzdata\\Zulu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tcl8.6\\word.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('tk\\tk8.6\\bgerror.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tk\\tk8.6\\button.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\tk8.6\\choosedir.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tk\\tk8.6\\clrpick.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tk\\tk8.6\\comdlg.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tk\\tk8.6\\console.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\README',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\README',
   'DATA'),
  ('tk\\tk8.6\\demos\\anilabel.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\anilabel.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\aniwave.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\aniwave.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\arrow.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\arrow.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\bind.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\bind.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\bitmap.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\bitmap.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\browse',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\browse',
   'DATA'),
  ('tk\\tk8.6\\demos\\button.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\button.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\check.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\check.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\clrpick.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\clrpick.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\colors.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\colors.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\combo.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\combo.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\cscroll.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\cscroll.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ctext.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ctext.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\dialog1.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\dialog1.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\dialog2.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\dialog2.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\en.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\en.msg',
   'DATA'),
  ('tk\\tk8.6\\demos\\entry1.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\entry1.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\entry2.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\entry2.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\entry3.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\entry3.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\filebox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\filebox.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\floor.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\floor.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\fontchoose.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\fontchoose.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\form.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\form.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\goldberg.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\goldberg.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\hello',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\hello',
   'DATA'),
  ('tk\\tk8.6\\demos\\hscale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\hscale.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\icon.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\icon.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\image1.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\image1.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\image2.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\image2.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\earth.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\earth.gif',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\earthmenu.png',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\earthmenu.png',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\earthris.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\earthris.gif',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\flagdown.xbm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\flagdown.xbm',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\flagup.xbm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\flagup.xbm',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\gray25.xbm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\gray25.xbm',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\letters.xbm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\letters.xbm',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\noletter.xbm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\noletter.xbm',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\ouster.png',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\ouster.png',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\pattern.xbm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\pattern.xbm',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\tcllogo.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\tcllogo.gif',
   'DATA'),
  ('tk\\tk8.6\\demos\\images\\teapot.ppm',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\images\\teapot.ppm',
   'DATA'),
  ('tk\\tk8.6\\demos\\items.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\items.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ixset',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ixset',
   'DATA'),
  ('tk\\tk8.6\\demos\\knightstour.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\knightstour.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\label.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\label.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\labelframe.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\labelframe.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\license.terms',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\license.terms',
   'DATA'),
  ('tk\\tk8.6\\demos\\mclist.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\mclist.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\menu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\menu.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\menubu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\menubu.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\msgbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\msgbox.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\nl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\nl.msg',
   'DATA'),
  ('tk\\tk8.6\\demos\\paned1.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\paned1.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\paned2.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\paned2.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\pendulum.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\pendulum.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\plot.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\plot.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\puzzle.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\puzzle.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\radio.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\radio.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\rmt',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\rmt',
   'DATA'),
  ('tk\\tk8.6\\demos\\rolodex',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\rolodex',
   'DATA'),
  ('tk\\tk8.6\\demos\\ruler.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ruler.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\sayings.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\sayings.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\search.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\search.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\spin.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\spin.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\square',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\square',
   'DATA'),
  ('tk\\tk8.6\\demos\\states.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\states.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\style.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\style.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\tclIndex',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\tclIndex',
   'DATA'),
  ('tk\\tk8.6\\demos\\tcolor',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\tcolor',
   'DATA'),
  ('tk\\tk8.6\\demos\\text.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\text.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\textpeer.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\textpeer.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\timer',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\timer',
   'DATA'),
  ('tk\\tk8.6\\demos\\toolbar.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\toolbar.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\tree.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\tree.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ttkbut.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ttkbut.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ttkmenu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ttkmenu.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ttknote.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ttknote.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ttkpane.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ttkpane.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ttkprogress.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ttkprogress.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\ttkscale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\ttkscale.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\twind.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\twind.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\unicodeout.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\unicodeout.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\vscale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\vscale.tcl',
   'DATA'),
  ('tk\\tk8.6\\demos\\widget',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\demos\\widget',
   'DATA'),
  ('tk\\tk8.6\\dialog.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tk\\tk8.6\\entry.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tk\\tk8.6\\focus.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('tk\\tk8.6\\fontchooser.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\tk8.6\\iconlist.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tk\\tk8.6\\icons.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('tk\\tk8.6\\images\\README',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tk\\tk8.6\\images\\logo.eps',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tk\\tk8.6\\images\\logo100.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\logo64.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\logoLarge.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\logoMed.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\pwrdLogo.eps',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tk\\tk8.6\\images\\pwrdLogo100.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\pwrdLogo150.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\pwrdLogo175.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\pwrdLogo200.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\pwrdLogo75.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\tk8.6\\images\\tai-ku.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tk\\tk8.6\\license.terms',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\tk8.6\\listbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tk\\tk8.6\\megawidget.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tk\\tk8.6\\menu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('tk\\tk8.6\\mkpsenc.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tk\\tk8.6\\msgbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tk\\tk8.6\\msgs\\cs.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\da.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\de.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\el.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\en.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\en_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\eo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\fr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\hu.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\it.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\nl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\pl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\pt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\ru.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\tk8.6\\msgs\\sv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\tk8.6\\obsolete.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\tk8.6\\optMenu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tk\\tk8.6\\palette.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tk\\tk8.6\\panedwindow.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\tk8.6\\pkgIndex.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tk\\tk8.6\\safetk.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tk\\tk8.6\\scale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('tk\\tk8.6\\scrlbar.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tk\\tk8.6\\spinbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tk\\tk8.6\\tclIndex',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tk\\tk8.6\\tearoff.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tk\\tk8.6\\text.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('tk\\tk8.6\\tk.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tk\\tk8.6\\tkfbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\altTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\aquaTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\button.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\clamTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\classicTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\combobox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\cursors.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\defaults.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\entry.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\fonts.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\menubutton.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\notebook.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\panedwindow.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\progress.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\scale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\scrollbar.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\sizegrip.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\spinbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\treeview.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\ttk.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\utils.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\vistaTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\winTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\ttk\\xpTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tk\\tk8.6\\unsupported.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\tk8.6\\xmfbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\Work__\\Python_Version\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\Work__\\Python_Version\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('wheel-0.38.4.dist-info\\INSTALLER',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.38.4.dist-info\\RECORD',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\top_level.txt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\LICENSE',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\LICENSE',
   'DATA'),
  ('wheel-0.38.4.dist-info\\WHEEL',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.38.4.dist-info\\METADATA',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.38.4.dist-info\\entry_points.txt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\WHEEL',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.38.4.dist-info\\top_level.txt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\INSTALLER',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\RECORD',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\entry_points.txt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.1.dist-info\\METADATA',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\setuptools-65.5.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.38.4.dist-info\\LICENSE.txt',
   'D:\\Work__\\Pycharm_Work\\http3_python\\venv\\Lib\\site-packages\\wheel-0.38.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'D:\\Work__\\Pycharm_Work\\http3_python\\build\\MangaPDFConverterV2\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
