<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="455342e1-7162-4ce5-b235-15a38062a9eb" name="更改" comment="更改">
      <change afterPath="$PROJECT_DIR$/aioquic/h3_client_qpack.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/aioquic/examples/http3_client.py" beforeDir="false" afterPath="$PROJECT_DIR$/aioquic/examples/http3_client.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/aioquic" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2mef3nIQwSUqkKrNM4fkhyJvXCX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Work__/Pycharm_Work/http3_python&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;two.files.diff.last.used.file&quot;: &quot;D:/Work__/Pycharm_Work/http3_python/aioquic/h3_client_qpack_v1.py&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Work__\Pycharm_Work\http3_python" />
      <recent name="D:\Work__\Pycharm_Work\http3_python\aioquic" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Work__\Pycharm_Work\http3_python\manga" />
      <recent name="D:\Work__\Pycharm_Work\http3_python\aioquic" />
    </key>
  </component>
  <component name="RunManager" selected="Python.CompileTheoryExp2">
    <configuration name="CompileTheoryExp2" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="http3_python" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Compile_test" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\Work__\Pycharm_Work\http3_python\Compile_test\CompileTheoryExp2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="h3_client_qpack_v1_1_ali" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="http3_python" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/aioquic" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/aioquic/h3_client_qpack_v1_1_ali.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="manga_converter_gui_1" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="http3_python" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\Work__\Pycharm_Work\http3_python\manga\manga_converter_gui_1.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="manga_to_pdf" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="http3_python" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\Work__\Pycharm_Work\http3_python\manga\manga_to_pdf.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="recursive_manga_to_pdf" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="http3_python" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\Work__\Pycharm_Work\http3_python\manga\recursive_manga_to_pdf.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.recursive_manga_to_pdf" />
        <item itemvalue="Python.manga_to_pdf" />
        <item itemvalue="Python.manga_converter_gui_1" />
        <item itemvalue="Python.h3_client_qpack_v1_1_ali" />
        <item itemvalue="Python.CompileTheoryExp2" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="455342e1-7162-4ce5-b235-15a38062a9eb" name="更改" comment="" />
      <created>1727441766163</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727441766163</updated>
      <workItem from="1727441768330" duration="284000" />
      <workItem from="1727675278848" duration="2026000" />
      <workItem from="1727679827431" duration="871000" />
      <workItem from="1727870325766" duration="3659000" />
      <workItem from="1728395853720" duration="13768000" />
      <workItem from="1729047325254" duration="15275000" />
      <workItem from="1729611290462" duration="12950000" />
      <workItem from="1730265407402" duration="40655000" />
      <workItem from="1731431366991" duration="1201000" />
      <workItem from="1732678611346" duration="1999000" />
      <workItem from="1735798223486" duration="5036000" />
      <workItem from="1736919700471" duration="1431000" />
      <workItem from="1737522761651" duration="5734000" />
      <workItem from="1737873939083" duration="9265000" />
      <workItem from="1740118825713" duration="1738000" />
      <workItem from="1740543995186" duration="3031000" />
      <workItem from="1741010620700" duration="1204000" />
      <workItem from="1742143958904" duration="1433000" />
      <workItem from="1747238203416" duration="2361000" />
      <workItem from="1747379005712" duration="2480000" />
      <workItem from="1747898201502" duration="4904000" />
      <workItem from="1748839065205" duration="10270000" />
      <workItem from="1749906397129" duration="698000" />
      <workItem from="1750140508917" duration="2529000" />
      <workItem from="1750145951816" duration="29000" />
      <workItem from="1750146342669" duration="48000" />
      <workItem from="1750146400767" duration="370000" />
      <workItem from="1750147055511" duration="967000" />
      <workItem from="1750841083284" duration="1406000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="更改" />
    <option name="LAST_COMMIT_MESSAGE" value="更改" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/http3_python$h3_client2.coverage" NAME="h3_client2 Coverage Results" MODIFIED="1729613422845" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack_v2.coverage" NAME="h3_client_qpack_v2 Coverage Results" MODIFIED="1730879080018" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$qpack_google.coverage" NAME="qpack_google Coverage Results" MODIFIED="1747250378066" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack_v1.coverage" NAME="h3_client_qpack_v1 Coverage Results" MODIFIED="1730820223970" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$manga_converter_gui.coverage" NAME="manga_converter_gui Coverage Results" MODIFIED="1748862796941" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/http3_python$recursive_manga_to_pdf.coverage" NAME="recursive_manga_to_pdf Coverage Results" MODIFIED="1748844600008" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/http3_python$draw_test.coverage" NAME="draw_test Coverage Results" MODIFIED="1740119595345" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack_v1_1_ten.coverage" NAME="h3_client_qpack_v1_1_ten Coverage Results" MODIFIED="1737527062125" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$manga_to_pdf.coverage" NAME="manga_to_pdf Coverage Results" MODIFIED="1748842952652" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack_v1_1_ali.coverage" NAME="h3_client_qpack_v1_1_ali Coverage Results" MODIFIED="1748839116353" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$Epub2Pdf.coverage" NAME="Epub2Pdf Coverage Results" MODIFIED="1728793177712" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$draw1.coverage" NAME="draw1 Coverage Results" MODIFIED="1737522829496" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$test_uncompyle6.coverage" NAME="test_uncompyle6 Coverage Results" MODIFIED="1727873436292" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack.coverage" NAME="h3_client_qpack Coverage Results" MODIFIED="1730362310652" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$ads_deal.coverage" NAME="ads_deal Coverage Results" MODIFIED="1742145202949" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Book_deal" />
    <SUITE FILE_PATH="coverage/http3_python$CompileTheory.coverage" NAME="CompileTheory Coverage Results" MODIFIED="1748873743109" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Compile_test" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack_v1_1_baidu.coverage" NAME="h3_client_qpack_v1_1_baidu Coverage Results" MODIFIED="1738049405046" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client3.coverage" NAME="h3_client3 Coverage Results" MODIFIED="1730269381294" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$draw2.coverage" NAME="draw2 Coverage Results" MODIFIED="1741156649303" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
    <SUITE FILE_PATH="coverage/http3_python$h3_client_qpack_v1_1.coverage" NAME="h3_client_qpack_v1_1 Coverage Results" MODIFIED="1737525349765" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/aioquic" />
  </component>
</project>