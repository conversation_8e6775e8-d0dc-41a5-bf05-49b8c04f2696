# 安装所需的库
# !pip install ebooklib reportlab openai

import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib import fonts
from reportlab.lib.styles import getSampleStyleSheet
import openai

# 设置你的OpenAI API密钥
openai.api_key = 'sk-fjjGEVUxnAMZDGYnzCjZ4xOzH7A8Y8NxyvqYOuSGWio4SItd'

# 从EPUB文件中提取文本的函数
def extract_epub_text(epub_path):
    book = epub.read_epub(epub_path)
    chapters = []

    for item in book.get_items_of_type(ebooklib.ITEM_DOCUMENT):
        content = item.get_body_content()
        soup = BeautifulSoup(content, 'html.parser')

        # 查找章节标题和文本内容（基于HTML结构）
        for h1 in soup.find_all(['h1', 'h2']):  # 假设h1和h2是章节标题
            chapter_title = h1.get_text()
            chapter_content = h1.find_next_sibling('p').get_text()
            chapters.append((chapter_title, chapter_content))

    return chapters

# 将古文翻译为现代汉语的函数
def translate_text(text):
    response = openai.Completion.create(
        engine="text-davinci-003",
        prompt=f"将以下古文翻译为现代汉语：\n\n{text}",
        max_tokens=2500
    )
    return response.choices[0].text.strip()

# 创建PDF的函数
def create_pdf(translated_chapters, output_path):
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4

    for title, content in translated_chapters:
        c.setFont("Helvetica-Bold", 18)  # 章节标题的较大字体
        c.drawString(100, height - 100, title)  # 标题位置

        c.setFont("Helvetica", 12)  # 正文的正常字体
        text_object = c.beginText(100, height - 130)  # 从标题以下开始书写

        # 添加翻译后的内容
        for line in content.split("\n"):
            text_object.textLine(line)

        c.drawText(text_object)
        c.showPage()  # 为下一章创建新页面

    c.save()

# 处理EPUB文件转换为PDF的主要函数
def epub_to_pdf(epub_file, output_pdf):
    # 步骤1：从EPUB文件中提取章节
    chapters = extract_epub_text(epub_file)

    # 步骤2：翻译每个章节的内容
    translated_chapters = []
    for title, content in chapters:
        print(f"正在翻译章节：{title}")
        translated_content = translate_text(content)
        translated_chapters.append((title, translated_content))

    # 步骤3：创建包含翻译内容的PDF
    create_pdf(translated_chapters, output_pdf)
    print(f"PDF保存到：{output_pdf}")

# 使用示例
epub_file_path = '反经.epub'  # EPUB文件的路径
output_pdf_path = '反经_白话文翻译.pdf'  # 输出PDF文件的路径

# 运行转换过程
epub_to_pdf(epub_file_path, output_pdf_path)