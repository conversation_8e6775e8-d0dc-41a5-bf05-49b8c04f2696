<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WebServers">
    <option name="servers">
      <webServer id="58adc062-a53f-4aba-9eee-3588aa15fe24" name="originLzy">
        <fileTransfer accessType="SFTP" host="***************" port="22" sshConfigId="e0a0eb3c-5c9d-431e-b947-91c1164e583d" sshConfig="ubuntu@***************:22 key" keyPair="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" keepAliveTimeout="0" passiveMode="true" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="18c86d48-29b8-46f2-aec9-9a4ac26d2963" name="origin2">
        <fileTransfer accessType="SFTP" host="**************" port="22" sshConfigId="652d1e2e-f7ec-4f4b-a574-71fa4c18e714" sshConfig="ubuntu@**************:22 key" keyPair="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" keepAliveTimeout="0" passiveMode="true" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
    </option>
  </component>
</project>