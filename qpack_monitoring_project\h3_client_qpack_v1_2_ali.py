import argparse
import asyncio
import logging
import os
import pickle
import ssl
import time
from collections import deque, defaultdict
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast, Tuple
from urllib.parse import urlparse, urlencode
from datetime import datetime
import json

import aioquic
import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
    Headers,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket

try:
    import uvloop
except ImportError:
    uvloop = None
import uuid


logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/" + aioquic.__version__


class QpackCompressionStats:
    """QPACK 压缩统计信息类"""
    
    def __init__(self):
        self.request_count = 0
        self.total_original_bytes = 0
        self.total_compressed_bytes = 0
        self.total_encoder_bytes = 0
        self.header_frequency = defaultdict(int)  # 头部字段出现频率
        self.compression_history = []  # 每次请求的压缩数据
        self.encoder_bytes_history = []  # 编码器字节数历史
        self.dynamic_table_evidence = []  # 动态表使用证据
        
    def record_encoding(self, headers: Headers, original_size: int, 
                       compressed_size: int, encoder_bytes: int, 
                       encoder_bytes_sent_before: int, stream_id: int):
        """记录一次编码操作的统计信息"""
        self.request_count += 1
        self.total_original_bytes += original_size
        self.total_compressed_bytes += compressed_size
        self.total_encoder_bytes += encoder_bytes
        
        # 记录头部字段频率
        for name, value in headers:
            header_key = f"{name.decode('utf-8', errors='ignore')}:{value.decode('utf-8', errors='ignore')}"
            self.header_frequency[header_key] += 1
        
        # 计算压缩比率
        compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0
        
        # 记录本次压缩历史
        compression_record = {
            'request_id': self.request_count,
            'stream_id': stream_id,
            'original_size': original_size,
            'compressed_size': compressed_size,
            'encoder_bytes': encoder_bytes,
            'compression_ratio': compression_ratio,
            'encoder_bytes_sent_total': encoder_bytes_sent_before + encoder_bytes,
            'timestamp': time.time()
        }
        self.compression_history.append(compression_record)
        self.encoder_bytes_history.append(encoder_bytes_sent_before + encoder_bytes)
        
        # 分析动态表使用证据
        self._analyze_dynamic_table_usage(headers, compression_record)
        
        return compression_record
    
    def _analyze_dynamic_table_usage(self, headers: Headers, compression_record: dict):
        """分析动态表使用情况"""
        # 检查是否有重复的头部字段
        repeated_headers = []
        for name, value in headers:
            header_key = f"{name.decode('utf-8', errors='ignore')}:{value.decode('utf-8', errors='ignore')}"
            if self.header_frequency[header_key] > 1:
                repeated_headers.append(header_key)
        
        # 如果有重复头部且压缩效果好，可能使用了动态表
        if repeated_headers and compression_record['compression_ratio'] > 30:
            evidence = {
                'request_id': self.request_count,
                'repeated_headers': repeated_headers,
                'compression_ratio': compression_record['compression_ratio'],
                'encoder_bytes': compression_record['encoder_bytes'],
                'evidence_type': 'repeated_headers_good_compression'
            }
            self.dynamic_table_evidence.append(evidence)
    
    def get_compression_summary(self) -> dict:
        """获取压缩效果摘要"""
        if self.request_count == 0:
            return {}
        
        total_savings = self.total_original_bytes - self.total_compressed_bytes
        overall_compression_ratio = (total_savings / self.total_original_bytes) * 100
        
        return {
            'total_requests': self.request_count,
            'total_original_bytes': self.total_original_bytes,
            'total_compressed_bytes': self.total_compressed_bytes,
            'total_encoder_bytes': self.total_encoder_bytes,
            'total_savings_bytes': total_savings,
            'overall_compression_ratio': overall_compression_ratio,
            'average_original_size': self.total_original_bytes / self.request_count,
            'average_compressed_size': self.total_compressed_bytes / self.request_count,
            'dynamic_table_evidence_count': len(self.dynamic_table_evidence)
        }
    
    def print_detailed_report(self):
        """打印详细的压缩报告"""
        summary = self.get_compression_summary()
        
        print("\n" + "="*80)
        print("QPACK 动态表压缩效果详细报告")
        print("="*80)
        
        if not summary:
            print("没有压缩数据可报告")
            return
        
        print(f"总请求数: {summary['total_requests']}")
        print(f"原始头部总大小: {summary['total_original_bytes']} 字节")
        print(f"压缩后总大小: {summary['total_compressed_bytes']} 字节")
        print(f"编码器更新总字节: {summary['total_encoder_bytes']} 字节")
        print(f"总节省字节数: {summary['total_savings_bytes']} 字节")
        print(f"整体压缩比率: {summary['overall_compression_ratio']:.2f}%")
        print(f"平均原始大小: {summary['average_original_size']:.1f} 字节/请求")
        print(f"平均压缩后大小: {summary['average_compressed_size']:.1f} 字节/请求")
        
        print(f"\n动态表使用证据数量: {summary['dynamic_table_evidence_count']}")
        
        # 显示压缩效果趋势
        if len(self.compression_history) >= 5:
            print("\n压缩效果趋势分析:")
            first_5_avg = sum(r['compression_ratio'] for r in self.compression_history[:5]) / 5
            last_5_avg = sum(r['compression_ratio'] for r in self.compression_history[-5:]) / 5
            improvement = last_5_avg - first_5_avg
            print(f"前5次请求平均压缩比: {first_5_avg:.2f}%")
            print(f"后5次请求平均压缩比: {last_5_avg:.2f}%")
            print(f"压缩效果改善: {improvement:.2f}%")
        
        # 显示最频繁的头部字段
        print("\n最频繁的头部字段 (Top 10):")
        sorted_headers = sorted(self.header_frequency.items(), key=lambda x: x[1], reverse=True)
        for i, (header, count) in enumerate(sorted_headers[:10]):
            print(f"  {i+1}. {header} (出现 {count} 次)")
        
        # 显示动态表使用证据
        if self.dynamic_table_evidence:
            print(f"\n动态表使用证据详情:")
            for evidence in self.dynamic_table_evidence:
                print(f"  请求 #{evidence['request_id']}: 压缩比 {evidence['compression_ratio']:.1f}%, "
                      f"重复头部: {len(evidence['repeated_headers'])} 个")


class MonitoredH3Connection(H3Connection):
    """带监控功能的 H3Connection"""
    
    def __init__(self, quic, enable_webtransport: bool = False):
        super().__init__(quic, enable_webtransport)
        self.qpack_stats = QpackCompressionStats()
    
    def _calculate_headers_size(self, headers: Headers) -> int:
        """计算头部的原始大小（未压缩）"""
        total_size = 0
        for name, value in headers:
            # HTTP/2 风格的头部大小计算：name_len + value_len + 32 (overhead)
            total_size += len(name) + len(value) + 32
        return total_size
    
    def _encode_headers(self, stream_id: int, headers: Headers) -> bytes:
        """重写头部编码方法以添加监控功能"""
        # 计算原始头部大小
        original_size = self._calculate_headers_size(headers)
        
        # 记录编码前的编码器状态
        encoder_bytes_before = self._encoder_bytes_sent
        
        # 执行原始编码 - 调用父类方法但不发送编码器数据
        encoder_data, frame_data = self._encoder.encode(stream_id, headers)
        
        # 记录编码统计信息
        compression_record = self.qpack_stats.record_encoding(
            headers, original_size, len(frame_data), 
            len(encoder_data), encoder_bytes_before, stream_id
        )
        
        # 打印实时监控信息
        print(f"\n📊 请求 #{compression_record['request_id']} (Stream {stream_id}) QPACK 编码分析:")
        print(f"   原始头部大小: {original_size} 字节")
        print(f"   压缩后大小: {len(frame_data)} 字节")
        print(f"   编码器更新: {len(encoder_data)} 字节")
        print(f"   压缩比率: {compression_record['compression_ratio']:.2f}%")
        print(f"   节省字节: {original_size - len(frame_data)} 字节")
        
        # 继续原始流程 - 发送编码器数据
        self._encoder_bytes_sent += len(encoder_data)
        if encoder_data:
            self._quic.send_stream_data(self._local_encoder_stream_id, encoder_data)
        
        return frame_data


class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)

        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            headers: Optional[Dict] = None,
    ) -> None:
        if headers is None:
            headers = {}

        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class WebSocket:
    def __init__(
            self, http: HttpConnection, stream_id: int, transmit: Callable[[], None]
    ) -> None:
        """
        WebSocket对象的构造函数

        :param http: HTTP连接对象
        :param stream_id: WebSocket所在的流ID
        :param transmit: 一个可被调用的对象，调用时将使得HTTP连接对象
                         立即将缓存中的数据发送出去
        """
        self.http: HttpConnection = http  # HTTP连接对象
        self.queue: asyncio.Queue[str] = asyncio.Queue()  # 一个队列，用于保存
        # 从WebSocket中
        # 接收到的信息
        self.stream_id: int = stream_id  # WebSocket所在的流ID
        self.subprotocol: Optional[str] = None  # WebSocket的子协议
        self.transmit: Callable[[], None] = transmit  # 一个可被调用的对象，
        # 调用时将使得HTTP
        # 连接对象立即将
        # 缓存中的数据
        # 发送出去
        self.websocket: wsproto.Connection = wsproto.Connection(
            wsproto.ConnectionType.CLIENT
        )  # 一个WebSocket连接对象

    async def close(self, code: int = 1000, reason: str = "") -> None:
        """
        Perform the closing handshake.
        """
        data = self.websocket.send(
            wsproto.events.CloseConnection(code=code, reason=reason)
        )
        self.http.send_data(stream_id=self.stream_id, data=data, end_stream=True)
        self.transmit()

    async def recv(self) -> str:
        """
        Receive the next message.
        """
        return await self.queue.get()

    async def send(self, message: str) -> None:
        """
        Send a message.
        """
        assert isinstance(message, str)

        data = self.websocket.send(wsproto.events.TextMessage(data=message))
        self.http.send_data(stream_id=self.stream_id, data=data, end_stream=False)
        self.transmit()

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, HeadersReceived):
            for header, value in event.headers:
                if header == b"sec-websocket-protocol":
                    self.subprotocol = value.decode()
        elif isinstance(event, DataReceived):
            self.websocket.receive_data(event.data)

        for ws_event in self.websocket.events():
            self.websocket_event_received(ws_event)

    def websocket_event_received(self, event: wsproto.events.Event) -> None:
        if isinstance(event, wsproto.events.TextMessage):
            self.queue.put_nowait(event.data)


class MonitoredHttpClient(QuicConnectionProtocol):
    """带 QPACK 监控功能的 HTTP 客户端"""

    def __init__(self, *args, **kwargs) -> None:
        """
        HTTP客户端协议 - 增强版，支持 QPACK 压缩监控
        """
        super().__init__(*args, **kwargs)
        #: 一个字典，key是流ID，value是一个队列，保存该流ID
        #: 对应的推送事件
        self.pushes: Dict[int, Deque[H3Event]] = {}
        #: 一个HTTP连接对象 - 使用监控版本
        self._http: Optional[HttpConnection] = None

        #: 一个字典，key是流ID，value是一个队列，保存该流ID
        #: 对应的请求响应事件
        self._request_events: Dict[int, Deque[H3Event]] = {}

        #: 一个字典，key是流ID，value是一个Future对象，对应
        #: 该流ID的请求响应事件
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}

        #: 一个字典，key是流ID，value是一个WebSocket对象
        self._websockets: Dict[int, WebSocket] = {}

        #: 选择使用H0还是H3 - 使用监控版本的 H3Connection
        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            # 使用带监控功能的 H3Connection
            self._http = MonitoredH3Connection(self._quic)

    async def get(self, url: str, headers: Optional[Dict] = None) -> Deque[H3Event]:
        """
        Perform a GET request.
        """
        return await self._request(
            HttpRequest(method="GET", url=URL(url), headers=headers)
        )

    async def post(
            self, url: str, data: bytes, headers: Optional[Dict] = None
    ) -> Deque[H3Event]:
        """
        Perform a POST request.
        """
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def websocket(
            self, url: str, subprotocols: Optional[List[str]] = None
    ) -> WebSocket:
        """
        Open a WebSocket.
        """
        request = HttpRequest(method="CONNECT", url=URL(url))
        stream_id = self._quic.get_next_available_stream_id()
        websocket = WebSocket(
            http=self._http, stream_id=stream_id, transmit=self.transmit
        )

        self._websockets[stream_id] = websocket

        headers = [
            (b":method", b"CONNECT"),
            (b":scheme", b"https"),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b":protocol", b"websocket"),
            (b"user-agent", USER_AGENT.encode()),
            (b"sec-websocket-version", b"13"),
        ]
        if subprotocols:
            headers.append(
                (b"sec-websocket-protocol", ", ".join(subprotocols).encode())
            )
        self._http.send_headers(stream_id=stream_id, headers=headers)

        self.transmit()

        return websocket

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                # http
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))

            elif stream_id in self._websockets:
                # websocket
                websocket = self._websockets[stream_id]
                websocket.http_event_received(event)

            elif event.push_id in self.pushes:
                # push
                self.pushes[event.push_id].append(event)

        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        #  pass event to the HTTP layer
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        stream_id = self._quic.get_next_available_stream_id()
        _headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        # 控制单个请求cookie的重复次数,使得第一次和第二次保持相同
        # 处理请求头，支持字典或列表
        if isinstance(request.headers, dict):
            request_headers = [(k.encode(), v.encode()) for k, v in request.headers.items()]
        else:
            request_headers = [(k.encode(), v.encode()) for k, v in request.headers]
        _headers += request_headers

        self._http.send_headers(
            stream_id=stream_id,
            headers=_headers,
            end_stream=not request.content,
        )
        if request.content:
            self._http.send_data(
                stream_id=stream_id, data=request.content, end_stream=True
            )

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        return await asyncio.shield(waiter)

    def get_qpack_stats(self) -> Optional[QpackCompressionStats]:
        """获取 QPACK 压缩统计信息"""
        if isinstance(self._http, MonitoredH3Connection):
            return self._http.qpack_stats
        return None


async def perform_http_request(
        client: MonitoredHttpClient,
        url: str,
        data: Optional[str],
        include: bool,
        output_dir: Optional[str],
) -> None:
    # perform request
    start = time.time()
    if data is not None:
        data_bytes = data.encode()
        http_events = await client.post(
            url,
            data=data_bytes,
            headers={
                "content-length": str(len(data_bytes)),
                "content-type": "application/x-www-form-urlencoded",
            },
        )
        method = "POST"
    else:
        http_events = await client.get(url)
        method = "GET"
    elapsed = time.time() - start

    # print speed
    octets = 0
    for http_event in http_events:
        if isinstance(http_event, DataReceived):
            octets += len(http_event.data)
    logger.info(
        "Response received for %s %s : %d bytes in %.1f s (%.3f Mbps)"
        % (method, urlparse(url).path, octets, elapsed, octets * 8 / elapsed / 1000000)
    )

    # output response
    if output_dir is not None:
        output_path = os.path.join(
            output_dir, os.path.basename(urlparse(url).path) or "index.html"
        )
        with open(output_path, "wb") as output_file:
            write_response(
                http_events=http_events, include=include, output_file=output_file
            )


def process_http_pushes(
        client: MonitoredHttpClient,
        include: bool,
        output_dir: Optional[str],
) -> None:
    for _, http_events in client.pushes.items():
        method = ""
        octets = 0
        path = ""
        for http_event in http_events:
            if isinstance(http_event, DataReceived):
                octets += len(http_event.data)
            elif isinstance(http_event, PushPromiseReceived):
                for header, value in http_event.headers:
                    if header == b":method":
                        method = value.decode()
                    elif header == b":path":
                        path = value.decode()
        logger.info("Push received for %s %s : %s bytes", method, path, octets)

        # output response
        if output_dir is not None:
            output_path = os.path.join(
                output_dir, os.path.basename(path) or "index.html"
            )
            with open(output_path, "wb") as output_file:
                write_response(
                    http_events=http_events, include=include, output_file=output_file
                )


def write_response(
        http_events: Deque[H3Event], output_file: BinaryIO, include: bool
) -> None:
    for http_event in http_events:
        if isinstance(http_event, HeadersReceived) and include:
            headers = b""
            for k, v in http_event.headers:
                headers += k + b": " + v + b"\r\n"
            if headers:
                output_file.write(headers + b"\r\n")
        elif isinstance(http_event, DataReceived):
            output_file.write(http_event.data)


def save_session_ticket(ticket: SessionTicket) -> None:
    """
    Callback which is invoked by the TLS engine when a new session ticket
    is received.
    """
    logger.info("New session ticket received")
    if args.session_ticket:
        with open(args.session_ticket, "wb") as fp:
            pickle.dump(ticket, fp)


async def main(configuration: QuicConfiguration):
    """主程序 - 增强版 QPACK 动态表压缩监控"""
    base_url = "https://aliyun.hawks.top"   #  可以用了
    # base_url = "https://baidu.snakin.top"   #  可以用了
    # base_url = "https://tencent.snakin.top"

    # 自行调整保证不要命中cache 可以用uuid的库自动生成
    incrementing_value = 0
    # 请求数量
    num_requests = 50  # 减少请求数量以便更好地观察动态表效果

    print("🚀 启动 QPACK 动态表压缩监控测试")
    print(f"📡 目标服务器: {base_url}")
    print(f"📊 计划请求数: {num_requests}")
    print("="*80)

    # Create a connection to the server
    async with connect(
            "aliyun.hawks.top",
            # "baidu.snakin.top",
            # "tencent.snakin.top",
            443,
            configuration=configuration,
            create_protocol=MonitoredHttpClient,  # 使用监控版本的客户端
    ) as client:
        client = cast(MonitoredHttpClient, client)

        # 等待QPACK动态表建立
        print("⏳ 等待 QPACK 动态表初始化...")
        await asyncio.sleep(1)  # 1000ms的初始延迟

        for request_num in range(num_requests):
            incrementing_value += 1
            random_uuid = uuid.uuid4()
            # 去除UUID中的横线，得到等长的随机字符串
            random_string = str(random_uuid).replace('-', '')

            # 方便一一对应查看
            query_params = {random_string: incrementing_value}
            url = f"{base_url}?{urlencode(query_params)}"

            print(f"\n🌐 发送请求 #{request_num + 1}: {url}")

            # 创建测试头部 - 包含一些重复的头部以测试动态表效果
            headers = []

            # 添加一些固定的头部（这些应该会被加入动态表）
            if request_num == 0:
                # 第一次请求：建立基础动态表条目
                headers = [
                    ('X-Custom-App', 'QPACK-Test-Application'),
                    ('X-Session-ID', 'session-12345'),
                    ('X-User-Agent-Extra', 'Mozilla/5.0 Custom'),
                    ('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8'),
                    ('Accept-Encoding', 'gzip, deflate, br'),
                ]
            else:
                # 后续请求：重复使用相同的头部以测试动态表压缩
                headers = [
                    ('X-Custom-App', 'QPACK-Test-Application'),  # 重复头部
                    ('X-Session-ID', 'session-12345'),  # 重复头部
                    ('X-User-Agent-Extra', 'Mozilla/5.0 Custom'),  # 重复头部
                    ('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8'),  # 重复头部
                    ('Accept-Encoding', 'gzip, deflate, br'),  # 重复头部
                    ('X-Request-ID', f'req-{request_num}'),  # 变化的头部
                ]

            # 添加一些大的头部来测试压缩效果
            if request_num >= 5:
                # 从第6个请求开始添加更多重复内容
                large_cookie = "sessiondata=" + "x" * 200 + f";requestid={request_num}"
                headers.append(('Cookie', large_cookie))
                headers.append(('X-Large-Header', 'A' * 100 + f'-{request_num}'))

            print(f"   📝 发送头部数量: {len(headers)}")

            try:
                response_events = await client.get(url, headers=headers)

                for event in response_events:
                    if isinstance(event, HeadersReceived):
                        # print(f"Received headers: {event.headers}")
                        continue
                    elif isinstance(event, DataReceived):
                        response_text = event.data.decode()
                        print(f"   ✅ 响应数据: {response_text[:100]}...")

                # 每10个请求显示一次中期统计
                if (request_num + 1) % 10 == 0:
                    stats = client.get_qpack_stats()
                    if stats:
                        summary = stats.get_compression_summary()
                        print(f"\n📈 中期统计 (前 {request_num + 1} 个请求):")
                        print(f"   总压缩比率: {summary['overall_compression_ratio']:.2f}%")
                        print(f"   累计节省: {summary['total_savings_bytes']} 字节")
                        print(f"   动态表证据: {summary['dynamic_table_evidence_count']} 条")

            except Exception as e:
                print(f"   ❌ 请求失败: {e}")

            # 在请求之间添加小延迟，确保QPACK状态同步
            if request_num < num_requests - 1:  # 最后一个请求不需要延迟
                await asyncio.sleep(0.5)  # 500ms的请求间隔

        # 显示最终的详细报告
        print("\n" + "🎯 测试完成！正在生成详细报告...")
        stats = client.get_qpack_stats()
        if stats:
            stats.print_detailed_report()

            # 保存统计数据到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            stats_file = f"qpack_stats_{timestamp}.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'summary': stats.get_compression_summary(),
                    'compression_history': stats.compression_history,
                    'header_frequency': dict(stats.header_frequency),
                    'dynamic_table_evidence': stats.dynamic_table_evidence
                }, f, indent=2, ensure_ascii=False)
            print(f"\n💾 详细统计数据已保存到: {stats_file}")
        else:
            print("❌ 无法获取 QPACK 统计信息")


if __name__ == "__main__":
    defaults = QuicConfiguration(is_client=True)

    parser = argparse.ArgumentParser(description="HTTP/3 client with QPACK monitoring")
    parser.add_argument(
        "--ca-certs", type=str, help="load CA certificates from the specified file"
    )
    parser.add_argument(
        "--certificate",
        type=str,
        help="load the TLS certificate from the specified file",
    )
    parser.add_argument(
        "--cipher-suites",
        type=str,
        help=(
            "only advertise the given cipher suites, e.g. `AES_256_GCM_SHA384,"
            "CHACHA20_POLY1305_SHA256`"
        ),
    )
    parser.add_argument(
        "--congestion-control-algorithm",
        type=str,
        default="reno",
        help="use the specified congestion control algorithm",
    )
    parser.add_argument(
        "-d", "--data", type=str, help="send the specified data in a POST request"
    )
    parser.add_argument(
        "-i",
        "--include",
        action="store_true",
        help="include the HTTP response headers in the output",
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="do not validate server certificate",
    )
    parser.add_argument(
        "--legacy-http",
        action="store_true",
        help="use HTTP/0.9",
    )
    parser.add_argument(
        "--max-data",
        type=int,
        help="connection-wide flow control limit (default: %d)" % defaults.max_data,
    )
    parser.add_argument(
        "--max-stream-data",
        type=int,
        help="per-stream flow control limit (default: %d)" % defaults.max_stream_data,
    )
    parser.add_argument(
        "--negotiate-v2",
        action="store_true",
        help="start with QUIC v1 and try to negotiate QUIC v2",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        help="write downloaded files to this directory",
    )
    parser.add_argument(
        "--private-key",
        type=str,
        help="load the TLS private key from the specified file",
    )
    parser.add_argument(
        "-q",
        "--quic-log",
        type=str,
        help="log QUIC events to QLOG files in the specified directory",
    )
    parser.add_argument(
        "-l",
        "--secrets-log",
        type=str,
        help="log secrets to a file, for use with Wireshark",
    )
    parser.add_argument(
        "-s",
        "--session-ticket",
        type=str,
        help="read and write session ticket from the specified file",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="increase logging verbosity"
    )
    parser.add_argument(
        "--local-port",
        type=int,
        default=0,
        help="local port to bind for connections",
    )
    parser.add_argument(
        "--max-datagram-size",
        type=int,
        default=defaults.max_datagram_size,
        help="maximum datagram size to send, excluding UDP or IP overhead",
    )
    parser.add_argument(
        "--zero-rtt", action="store_true", help="try to send requests using 0-RTT"
    )

    args = parser.parse_args()

    logging.basicConfig(
        format="%(asctime)s %(levelname)s %(name)s %(message)s",
        level=logging.DEBUG if args.verbose else logging.INFO,
    )

    if args.output_dir is not None and not os.path.isdir(args.output_dir):
        raise Exception("%s is not a directory" % args.output_dir)

    # prepare configuration
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H0_ALPN if args.legacy_http else H3_ALPN,
        congestion_control_algorithm=args.congestion_control_algorithm,
        max_datagram_size=args.max_datagram_size,
    )

    configuration.verify_mode = False  # 出于测试目的，不验证服务器证书

    # 设置 secrets_log_file 属性，用来记录密钥日志，解密抓到的pcap
    log_file_name = datetime.now().strftime("%Y%m%d_%H%M%S.log")
    logs_dir = "./logs/"
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    log_file_path = os.path.join("./logs/", log_file_name)

    configuration.secrets_log_file = open(log_file_path, "w")

    if args.ca_certs:
        configuration.load_verify_locations(args.ca_certs)
    if args.cipher_suites:
        configuration.cipher_suites = [
            CipherSuite[s] for s in args.cipher_suites.split(",")
        ]
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
    if args.max_data:
        configuration.max_data = args.max_data
    if args.max_stream_data:
        configuration.max_stream_data = args.max_stream_data
    if args.negotiate_v2:
        configuration.original_version = QuicProtocolVersion.VERSION_1
        configuration.supported_versions = [
            QuicProtocolVersion.VERSION_2,
            QuicProtocolVersion.VERSION_1,
        ]
    if args.quic_log:
        configuration.quic_logger = QuicFileLogger(args.quic_log)
    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
    if args.session_ticket:
        try:
            with open(args.session_ticket, "rb") as fp:
                configuration.session_ticket = pickle.load(fp)
        except FileNotFoundError:
            pass

    # load SSL certificate and key
    if args.certificate is not None:
        configuration.load_cert_chain(args.certificate, args.private_key)

    if uvloop is not None:
        uvloop.install()

    print("🎯 QPACK 动态表压缩监控测试程序")
    print("=" * 50)
    print("本程序将详细监控 HTTP/3 QPACK 动态表的使用情况")
    print("并提供压缩效果的量化证据")
    print("=" * 50)

    asyncio.run(main(configuration=configuration))
