#!/usr/bin/env python3
"""
QPACK 动态表压缩监控测试启动脚本
一键运行完整的测试、分析和可视化流程
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """打印项目横幅"""
    print("=" * 80)
    print("🎯 QPACK 动态表压缩监控测试系统")
    print("=" * 80)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📁 工作目录:", os.getcwd())
    print("🐍 Python版本:", sys.version.split()[0])
    print("=" * 80)

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = ['aioquic', 'matplotlib', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def run_qpack_test():
    """运行 QPACK 测试"""
    print("\n🚀 启动 QPACK 动态表压缩测试...")
    print("=" * 50)
    
    try:
        # 运行主测试程序
        result = subprocess.run([
            sys.executable, 
            "h3_client_qpack_v1_2_ali.py"
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n✅ QPACK 测试完成")
            return True
        else:
            print(f"\n❌ QPACK 测试失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ 运行测试时出错: {e}")
        return False

def run_analysis():
    """运行数据分析"""
    print("\n📊 开始数据分析...")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, 
            "analyze_qpack_results.py"
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n✅ 数据分析完成")
            return True
        else:
            print(f"\n❌ 数据分析失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ 运行分析时出错: {e}")
        return False

def run_visualization():
    """运行可视化"""
    print("\n🎨 生成可视化图表...")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, 
            "visualize_qpack_results.py"
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n✅ 可视化图表生成完成")
            return True
        else:
            print(f"\n❌ 可视化生成失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ 运行可视化时出错: {e}")
        return False

def show_results():
    """显示结果文件"""
    print("\n📁 生成的文件:")
    print("=" * 50)
    
    # 查找生成的文件
    files_to_check = [
        ("qpack_stats_*.json", "测试数据文件"),
        ("qpack_compression_trends.png", "压缩趋势图"),
        ("qpack_phase_comparison.png", "阶段对比图"),
        ("qpack_efficiency_analysis.png", "效率分析图"),
        ("qpack_summary_report.png", "总结报告图")
    ]
    
    import glob
    
    for pattern, description in files_to_check:
        files = glob.glob(pattern)
        if files:
            for file in files:
                if os.path.exists(file):
                    size = os.path.getsize(file)
                    print(f"  ✅ {file} ({size:,} 字节) - {description}")
        else:
            print(f"  ❌ {pattern} - {description} (未找到)")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺失的包后重试")
        return 1
    
    print("\n🎯 开始完整的 QPACK 监控测试流程...")
    
    # 步骤1: 运行 QPACK 测试
    if not run_qpack_test():
        print("\n❌ 测试失败，流程终止")
        return 1
    
    # 等待一下确保文件写入完成
    time.sleep(1)
    
    # 步骤2: 运行数据分析
    if not run_analysis():
        print("\n⚠️  数据分析失败，但继续执行可视化")
    
    # 步骤3: 运行可视化
    if not run_visualization():
        print("\n⚠️  可视化生成失败")
    
    # 显示结果
    show_results()
    
    # 最终总结
    print("\n" + "=" * 80)
    print("🎉 QPACK 动态表压缩监控测试流程完成！")
    print("=" * 80)
    print("📊 请查看生成的图表和数据文件了解详细结果")
    print("📖 详细说明请参考 README.md 文件")
    print("=" * 80)
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)
