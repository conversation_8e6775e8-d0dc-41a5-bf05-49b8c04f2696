#V2 增加全局输出目录

import tkinter as tk
from tkinter import filedialog, scrolledtext, messagebox, ttk
import os
import re
from PIL import Image
import threading
import shutil  # For copying files
import subprocess  # For opening folder


# --- Core PDF Creation Logic (adapted) ---
def create_manga_pdf_core(image_folder, output_filename_base, resolution, log_callback, global_output_dir=None):
    # PDF in source folder
    source_pdf_path = os.path.join(image_folder, output_filename_base)
    pdf_created_in_source = False

    if os.path.exists(source_pdf_path):
        log_callback(f"  源目录PDF已存在: {output_filename_base} 在 {os.path.basename(image_folder)}")
        pdf_created_in_source = True  # Assume it's valid for copying
    else:
        # --- Actual PDF creation logic (same as before) ---
        image_files_in_this_folder = []
        supported_formats = ('.jpg', '.jpeg', '.png')
        try:
            for f_name in os.listdir(image_folder):
                if f_name.lower().endswith(supported_formats):
                    image_files_in_this_folder.append(os.path.join(image_folder, f_name))
        except Exception as e:
            log_callback(f"  错误: 列出 '{os.path.basename(image_folder)}' 文件时出错: {e}")
            return "failed_listing"

        if not image_files_in_this_folder:
            log_callback(f"  信息: '{os.path.basename(image_folder)}' 中无图片文件。")
            return "no_images"

        def sort_key(filepath):
            filename = os.path.basename(filepath).lower()
            if filename == "cover.jpg": return (-1, "")
            match = re.match(r'i_(\d+)\.(jpg|jpeg|png)', filename)
            if match: return (0, int(match.group(1)))
            return (1, filename)

        image_files_in_this_folder.sort(key=sort_key)

        pil_images = []
        opened_images_refs = []
        try:
            log_callback(
                f"  为 '{os.path.basename(image_folder)}' 处理图片 (共 {len(image_files_in_this_folder)} 页)...")
            for i, f_path in enumerate(image_files_in_this_folder):
                img = Image.open(f_path)
                opened_images_refs.append(img)
                if img.mode == 'RGBA':
                    background = Image.new("RGB", img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[3])
                    img_to_add = background
                elif img.mode == 'P':
                    img_to_add = img.convert('RGB')
                elif img.mode == 'L':
                    img_to_add = img
                elif img.mode == 'RGB':
                    img_to_add = img
                else:
                    img_to_add = img.convert('RGB')
                pil_images.append(img_to_add)
        except Exception as e:
            log_callback(f"  错误: 在 '{os.path.basename(image_folder)}' 中打开/转换图片时出错: {e}")
            for opened_img in opened_images_refs: opened_img.close()
            return "failed_processing"
        if not pil_images:
            for opened_img in opened_images_refs: opened_img.close()
            return "no_images_after_processing"

        first_image_to_save = pil_images[0]
        other_images_to_save = pil_images[1:]
        try:
            log_callback(f"  保存PDF到源目录: {output_filename_base}")
            first_image_to_save.save(
                source_pdf_path, "PDF", resolution=resolution,
                save_all=True, append_images=other_images_to_save
            )
            log_callback(f"  PDF创建成功(源): {output_filename_base}")
            pdf_created_in_source = True
        except Exception as e:
            log_callback(f"  错误: 保存PDF到源目录 '{output_filename_base}' 时出错: {e}")
            return "failed_saving_source"
        finally:
            for opened_img in opened_images_refs: opened_img.close()

    # --- Copy to global output directory if specified and PDF was created/exists in source ---
    if global_output_dir and pdf_created_in_source:
        try:
            if not os.path.exists(global_output_dir):
                log_callback(f"  创建全局输出目录: {global_output_dir}")
                os.makedirs(global_output_dir, exist_ok=True)  # exist_ok=True is good practice

            # To avoid name clashes, prepend parent folder name if not already distinct
            parent_folder_name = os.path.basename(image_folder)
            # Sanitize parent_folder_name for use in filename
            safe_parent_name = re.sub(r'[\\/*?:"<>|]', "_", parent_folder_name)

            # Check if output_filename_base already contains the parent folder name (e.g. if it's the same)
            if safe_parent_name.lower() not in output_filename_base.lower():
                output_pdf_name_global = f"{safe_parent_name} - {output_filename_base}"
            else:  # filename already likely unique enough or is the folder name itself
                output_pdf_name_global = output_filename_base

            global_pdf_path = os.path.join(global_output_dir, output_pdf_name_global)

            if os.path.exists(global_pdf_path):
                log_callback(f"  全局输出目录PDF已存在: {output_pdf_name_global}")
                return "success_source_skipped_global"  # Indicate source was ok, global skipped

            log_callback(f"  复制PDF到全局输出: {output_pdf_name_global}")
            shutil.copy2(source_pdf_path, global_pdf_path)  # copy2 preserves metadata
            log_callback(f"  PDF复制成功(全局): {output_pdf_name_global}")
            return "success_both"
        except Exception as e:
            log_callback(f"  错误: 复制PDF到全局输出目录时出错: {e}")
            return "failed_copying_global"  # Source PDF was created/existed

    elif pdf_created_in_source:
        return "success_source_only"  # Global output not specified or some other condition

    return "failed_unknown"  # Should not happen if logic is correct


def process_directory_recursively_core(root_dir, resolution, global_output_dir, log_callback, progress_callback):
    log_callback(f"开始递归扫描根目录: {root_dir}")
    if global_output_dir:
        log_callback(f"全局输出目录设定为: {global_output_dir}")

    counts = {
        "success_both": 0, "success_source_only": 0, "success_source_skipped_global": 0,
        "skipped_source_exists": 0,  # This will be part of create_manga_pdf_core's logic
        "no_images": 0, "no_images_after_processing": 0,
        "failed_listing": 0, "failed_processing": 0, "failed_saving_source": 0,
        "failed_copying_global": 0, "failed_unknown": 0,
        "total_image_folders": 0
    }

    image_folders_to_process = []
    for dirpath, _, filenames in os.walk(root_dir):
        if any(fname.lower().endswith(('.jpg', '.jpeg', '.png')) for fname in filenames):
            image_folders_to_process.append(dirpath)
            counts["total_image_folders"] += 1

    if not image_folders_to_process:
        log_callback("在所选目录及其子目录中未找到包含图片的文件夹。")
        # No need to return counts here, let it proceed to summary
    else:
        log_callback(f"共找到 {counts['total_image_folders']} 个包含图片的文件夹准备处理。")

    progress_callback(0, counts["total_image_folders"])

    for i, dirpath in enumerate(image_folders_to_process):
        log_callback(f"\n处理文件夹 ({i + 1}/{counts['total_image_folders']}): {dirpath}")

        folder_basename = os.path.basename(dirpath)
        output_pdf_name_base = f"{folder_basename}.pdf"
        output_pdf_name_base = re.sub(r'[\\/*?:"<>|]', "_", output_pdf_name_base)

        # Call the core PDF creation and copying function
        # create_manga_pdf_core now handles both source and global output based on global_output_dir
        # If create_manga_pdf_core's first check (source_pdf_path exists) is true, it implies skipped_source_exists
        # We need to be careful how we count this.

        # Simplification: create_manga_pdf_core now returns more granular statuses
        # if os.path.exists(os.path.join(dirpath, output_pdf_name_base)) and (not global_output_dir or not os.path.exists(os.path.join(global_output_dir, ...))):
        #      # If only source exists and we are not copying, or global target doesn't exist
        #      counts["skipped_source_exists"] +=1

        result_status = create_manga_pdf_core(dirpath, output_pdf_name_base, resolution, log_callback,
                                              global_output_dir)
        counts[result_status] = counts.get(result_status, 0) + 1
        progress_callback(i + 1, counts["total_image_folders"])

    log_callback(f"\n--- 扫描完成 ---")
    log_callback(f"成功创建并复制到全局: {counts['success_both']}")
    log_callback(
        f"成功创建到源目录 (全局未指定或已存在): {counts['success_source_only'] + counts['success_source_skipped_global']}")
    # log_callback(f"源目录PDF已存在并跳过创建: {counts['skipped_source_exists']}") # This is now implicitly handled by create_manga_pdf_core status
    log_callback(f"文件夹无图片: {counts['no_images'] + counts['no_images_after_processing']}")
    failed_total = counts['failed_listing'] + counts['failed_processing'] + counts['failed_saving_source'] + counts[
        'failed_copying_global'] + counts['failed_unknown']
    log_callback(f"各类失败总计: {failed_total}")
    return counts


# --- Tkinter GUI Application ---
class MangaConverterApp:
    def __init__(self, root):
        self.root = root
        root.title("漫画文件夹转PDF转换器 v2")
        root.geometry("750x600")

        # --- Frame for Input Directory ---
        input_dir_frame = ttk.LabelFrame(root, text="选择输入根目录", padding=(10, 5))
        input_dir_frame.pack(padx=10, pady=5, fill="x")
        self.input_dir_path_var = tk.StringVar()
        ttk.Entry(input_dir_frame, textvariable=self.input_dir_path_var, width=70).pack(side=tk.LEFT, padx=(0, 5),
                                                                                        expand=True, fill="x")
        ttk.Button(input_dir_frame, text="浏览...", command=self.browse_input_directory).pack(side=tk.LEFT)

        # --- Frame for Output Directory ---
        output_dir_frame = ttk.LabelFrame(root, text="设定全局输出目录 (可选)", padding=(10, 5))
        output_dir_frame.pack(padx=10, pady=5, fill="x")
        self.output_dir_path_var = tk.StringVar()
        ttk.Entry(output_dir_frame, textvariable=self.output_dir_path_var, width=60).pack(side=tk.LEFT, padx=(0, 5),
                                                                                          expand=True, fill="x")
        ttk.Button(output_dir_frame, text="浏览...", command=self.browse_output_directory).pack(side=tk.LEFT,
                                                                                                padx=(0, 5))
        self.open_output_button = ttk.Button(output_dir_frame, text="打开输出目录", command=self.open_output_folder,
                                             state=tk.DISABLED)
        self.open_output_button.pack(side=tk.LEFT)

        # --- Frame for settings ---
        settings_frame = ttk.LabelFrame(root, text="设置", padding=(10, 5))
        settings_frame.pack(padx=10, pady=5, fill="x")
        ttk.Label(settings_frame, text="PDF分辨率 (DPI):").pack(side=tk.LEFT, padx=(0, 5))
        self.resolution_var = tk.StringVar(value="150")
        ttk.Entry(settings_frame, textvariable=self.resolution_var, width=10).pack(side=tk.LEFT)

        # --- Frame for actions ---
        action_frame = ttk.Frame(root, padding=(10, 5))
        action_frame.pack(padx=10, pady=5, fill="x")
        self.start_button = ttk.Button(action_frame, text="开始转换", command=self.start_conversion_thread)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        self.clear_log_button = ttk.Button(action_frame, text="清空日志", command=self.clear_log)
        self.clear_log_button.pack(side=tk.LEFT)

        # --- Progress Bar ---
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(root, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(padx=10, pady=5, fill="x")

        # --- Log area ---
        log_frame = ttk.LabelFrame(root, text="日志", padding=(10, 5))
        log_frame.pack(padx=10, pady=10, fill="both", expand=True)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15, state=tk.DISABLED)
        self.log_text.pack(fill="both", expand=True)

        # Bind output directory var to enable/disable open button
        self.output_dir_path_var.trace_add("write", self.update_open_output_button_state)

    def browse_input_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.input_dir_path_var.set(directory)
            self.log_message(f"输入目录已选: {directory}")

    def browse_output_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir_path_var.set(directory)
            self.log_message(f"全局输出目录已选: {directory}")
            # self.update_open_output_button_state() # trace will handle this

    def update_open_output_button_state(self, *args):
        if self.output_dir_path_var.get() and os.path.isdir(self.output_dir_path_var.get()):
            self.open_output_button.config(state=tk.NORMAL)
        else:
            self.open_output_button.config(state=tk.DISABLED)

    def open_output_folder(self):
        output_dir = self.output_dir_path_var.get()
        if output_dir and os.path.isdir(output_dir):
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(output_dir)
                elif os.name == 'posix':  # macOS, Linux
                    subprocess.call(('open', output_dir) if sys.platform == 'darwin' else ('xdg-open', output_dir))
                self.log_message(f"已尝试打开输出目录: {output_dir}")
            except Exception as e:
                self.log_message(f"无法打开输出目录 '{output_dir}': {e}")
                messagebox.showerror("错误", f"无法打开目录: {e}")
        elif output_dir:
            messagebox.showwarning("提示", f"输出目录 '{output_dir}' 不存在或不是一个有效目录。")
        else:
            messagebox.showinfo("提示", "请先设定一个输出目录。")

    def log_message(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

    def update_progress(self, current, total):
        if total > 0:
            self.progress_var.set((current / total) * 100)
        else:
            self.progress_var.set(0)
        self.root.update_idletasks()

    def clear_log(self):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.progress_var.set(0)

    def start_conversion_thread(self):
        root_dir = self.input_dir_path_var.get()
        global_output_dir = self.output_dir_path_var.get().strip()  # Strip whitespace

        if not root_dir:
            messagebox.showerror("错误", "请先选择一个输入根目录。")
            return
        if not os.path.isdir(root_dir):
            messagebox.showerror("错误", f"输入路径 '{root_dir}' 不是一个有效的目录。")
            return

        if global_output_dir and not os.path.exists(global_output_dir):
            # Ask user if they want to create it, or let the process create it
            # For simplicity, we'll let the process create it if it doesn't exist.
            # os.makedirs(global_output_dir, exist_ok=True) could be here,
            # but create_manga_pdf_core also handles it.
            pass
        elif global_output_dir and not os.path.isdir(global_output_dir):
            messagebox.showerror("错误", f"全局输出路径 '{global_output_dir}'存在但不是一个目录。")
            return

        try:
            resolution = float(self.resolution_var.get())
            if resolution <= 0: raise ValueError
        except ValueError:
            messagebox.showerror("错误", "请输入一个有效的正数作为分辨率。")
            return

        self.start_button.config(state=tk.DISABLED)
        self.clear_log_button.config(state=tk.DISABLED)
        self.open_output_button.config(state=tk.DISABLED)  # Disable during processing
        self.progress_var.set(0)

        thread = threading.Thread(target=self.run_conversion_logic,
                                  args=(root_dir, resolution, global_output_dir if global_output_dir else None))
        thread.daemon = True
        thread.start()

    def run_conversion_logic(self, root_dir, resolution, global_output_dir):
        try:
            self.log_message("转换开始...")
            process_directory_recursively_core(root_dir, resolution, global_output_dir, self.log_message,
                                               self.update_progress)
            self.log_message("转换完成！")
            messagebox.showinfo("完成", "所有文件夹处理完毕！")
        except Exception as e:
            self.log_message(f"发生严重错误: {e}")
            import traceback
            self.log_message(traceback.format_exc())  # Log full traceback for debugging
            messagebox.showerror("严重错误", f"处理过程中发生错误: {e}")
        finally:
            self.root.after(0, self.enable_buttons)

    def enable_buttons(self):
        self.start_button.config(state=tk.NORMAL)
        self.clear_log_button.config(state=tk.NORMAL)
        self.update_open_output_button_state()  # Re-evaluate based on output_dir_path_var


if __name__ == '__main__':
    # Removed the TCL_LIBRARY/TK_LIBRARY environment variable settings as per previous discussion.
    # PyInstaller should handle this with --add-data if needed.
    try:
        from PIL import _tkinter_finder
    except ImportError:
        pass

    root = tk.Tk()
    app = MangaConverterApp(root)
    root.mainloop()