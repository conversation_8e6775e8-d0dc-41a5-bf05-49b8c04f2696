import os
import re
from PIL import Image


def create_manga_pdf(image_folder, output_filename="manga_output.pdf"):
    if not os.path.isdir(image_folder):
        print(f"错误：文件夹 '{image_folder}' 不存在。")
        return

    image_files = []
    supported_formats = ('.jpg', '.jpeg', '.png')
    for f_name in os.listdir(image_folder):
        if f_name.lower().endswith(supported_formats):
            image_files.append(os.path.join(image_folder, f_name))

    if not image_files:
        print(f"文件夹 '{image_folder}' 中没有找到支持的图片文件。")
        return

    def sort_key(filepath):
        filename = os.path.basename(filepath).lower()
        if filename == "cover.jpg":
            return (-1, "")
        match = re.match(r'i_(\d+)\.(jpg|jpeg|png)', filename)
        if match:
            return (0, int(match.group(1)))
        return (1, filename)

    image_files.sort(key=sort_key)

    print("找到并排序的图片文件:")
    for i, f_path in enumerate(image_files):
        print(f"{i + 1}. {os.path.basename(f_path)}")

    pil_images = []
    opened_images_refs = []  # Keep track of opened images to close them

    try:
        for i, f_path in enumerate(image_files):
            print(f"正在处理: {os.path.basename(f_path)} ({i + 1}/{len(image_files)})")
            img = Image.open(f_path)
            opened_images_refs.append(img)  # Add to list for later closing

            # --- 改进的颜色模式处理 ---
            if img.mode == 'RGBA':
                # 如果有alpha通道，转换为RGB，背景通常为白色
                # 创建一个白色背景
                background = Image.new("RGB", img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[3])  # 3 is the alpha channel
                img_to_add = background
            elif img.mode == 'P':  # Paletted image
                # 尝试转换为 'L' (灰度) 如果它是单色的，否则转RGB
                # 一个简单的检查：如果调色板主要是灰阶，转为'L'可能更好
                # 但为了通用性，通常转RGB。如果确定是纯黑白漫画，可以考虑转'L'
                img_to_add = img.convert('RGB')  # 安全选项
                # 或者，如果确定是灰度：img_to_add = img.convert('L')
            elif img.mode == 'L':  # Grayscale
                img_to_add = img  # 保持L模式，对黑白漫画最佳
            elif img.mode == 'RGB':
                img_to_add = img  # 已经是RGB
            else:  # 其他模式，保险起见转RGB
                img_to_add = img.convert('RGB')

            pil_images.append(img_to_add)

    except Exception as e:
        print(f"打开或转换图片时出错: {e}")
        for opened_img in opened_images_refs:  # Close all images opened so far
            opened_img.close()
        return

    if not pil_images:
        print("没有图片可以添加到PDF。")
        for opened_img in opened_images_refs:  # Close all images opened so far
            opened_img.close()
        return

    # 第一个图像和其他图像分开处理以用于 save_all
    # 注意：pil_images现在包含的是转换后的图像副本，原始的img对象由opened_images_refs管理
    first_image_to_save = pil_images[0]
    other_images_to_save = pil_images[1:]

    output_pdf_path = os.path.join(image_folder, output_filename)

    try:
        print(f"\n正在保存PDF到: {output_pdf_path}")
        # --- 提高分辨率 ---
        # 漫画通常扫描DPI较高，尝试150, 200, 或 300
        # 较高的DPI意味着在PDF中，相同像素的图像物理尺寸更小，
        # 这在“适应页面”观看时，图像内容占页面的比例可能更大，减少“留白感”
        # 并且可以提高清晰度，减少“变灰”的感觉。
        first_image_to_save.save(
            output_pdf_path,
            "PDF",
            resolution=150.0,  # 尝试从100增加到150或更高
            save_all=True,
            append_images=other_images_to_save,
            # --- 尝试禁用PDF内部的JPEG压缩，如果图像已经是JPEG ---
            # Pillow的PDF编写器可能会对图像进行重新压缩。
            # 对于已经是JPEG的图像，这可能会导致质量下降。
            # 'quality' 参数主要用于JPEG压缩，PDF保存时可能不直接适用，
            # 但Pillow的底层行为可能仍会压缩。
            # 如果图片已经是高质量JPEG，我们希望避免二次压缩。
            # Pillow的PDF保存不直接提供不压缩JPEG的选项。
            # 如果图像是PNG或未压缩格式，PDF通常会用ZIP或JPEG压缩它们。
            # 我们可以尝试确保输入图像质量足够高。
            # 对于变灰问题，主要还是看颜色转换和分辨率。
        )
        print("PDF创建成功！")
    except Exception as e:
        print(f"保存PDF时出错: {e}")
    finally:
        # 关闭所有原始打开的图片对象
        for opened_img in opened_images_refs:
            opened_img.close()
        # pil_images 中的图像是转换后的，如果是副本，它们会被垃圾回收
        # 如果它们是原始图像的引用，也需要关闭。
        # 上面的 opened_images_refs 已经确保了原始文件句柄被关闭。


if __name__ == "__main__":
    folder = input("请输入包含单行本图片的文件夹路径: ")
    folder_basename = os.path.basename(folder.strip())
    if not folder_basename:
        folder_basename = os.path.basename(os.path.dirname(folder.strip()))

    output_pdf_name = f"{folder_basename}.pdf"
    output_pdf_name = re.sub(r'[\\/*?:"<>|]', "_", output_pdf_name)

    create_manga_pdf(folder.strip(), output_filename=output_pdf_name)