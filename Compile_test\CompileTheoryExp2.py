import re
from collections import defaultdict, OrderedDict


EPSILON = "ε"
DOLLAR = "$"  # End of input marker


class Grammar:
    def __init__(self, rules_str, augmented_start_symbol="S'", explicit_start_symbol=None):
        self.rules_str = rules_str
        self.augmented_start_symbol = augmented_start_symbol
        self.productions = OrderedDict()  # To keep production numbers consistent
        self.non_terminals = set()
        self.terminals = set()
        self.start_symbol = explicit_start_symbol  # This is the S in S' -> S
        self._parse_grammar()  # This will set self.start_symbol if it's None and not provided
        self._find_terminals_and_non_terminals()

        # For SLR table construction
        self.first_sets = defaultdict(set)
        self.follow_sets = defaultdict(set)
        self.lr0_items_sets = []  # Canonical collection of LR(0) items
        self.goto_map = {}  # (state_index, symbol) -> next_state_index
        self.action_table = {}  # (state_index, terminal) -> ('shift', state_idx) or ('reduce', prod_idx) or ('accept',)
        self.goto_table = {}  # (state_index, non_terminal) -> state_idx

    def _parse_grammar(self):
        """分析文法字符串，填充产生式，并确定起始符号"""
        # 如果没有明确指定起始符号，则尝试推断
        if self.start_symbol is None:
            potential_starts = ["program", "compilation_unit", "start_symbol"]  # 常见起始符号名称
            all_lhs_in_order = []
            # 预扫描以找到所有产生式的左侧非终结符
            for line in self.rules_str.splitlines():
                line = line.strip()
                if not line or line.startswith("#"): continue  # 跳过空行和注释

                lhs_part_str = ""
                if "→" in line:
                    lhs_part_str = line.split("→", 1)[0]
                elif "->" in line:
                    lhs_part_str = line.split("->", 1)[0]
                else:
                    continue  # 不是产生式规则
                all_lhs_in_order.append(lhs_part_str.strip())

            if all_lhs_in_order:
                found_start = False
                for ps in potential_starts:
                    if ps in all_lhs_in_order:
                        self.start_symbol = ps
                        found_start = True
                        break
                if not found_start:
                    # 回退：使用遇到的第一个产生式的 LHS 作为起始符号
                    self.start_symbol = all_lhs_in_order[0]

        if self.start_symbol is None:
            raise ValueError("无法确定文法的起始符号，且未明确提供。")

        # 添加增广产生式 S' -> S
        self.productions[0] = (self.augmented_start_symbol, [self.start_symbol])

        prod_idx = 1
        for line in self.rules_str.splitlines():
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            if "→" in line:
                lhs, rhs_part = line.split("→", 1)
            elif "->" in line:
                lhs, rhs_part = line.split("->", 1)
            else:
                continue

            lhs = lhs.strip()

            alternatives = rhs_part.split("|")
            for alt in alternatives:
                rhs = [s.strip() for s in alt.strip().split() if s.strip()]
                if not rhs:
                    rhs = [EPSILON]  # A -> ε
                self.productions[prod_idx] = (lhs, rhs)
                prod_idx += 1

    def _find_terminals_and_non_terminals(self):
        """从产生式中找出终结符和非终结符"""
        for lhs, _ in self.productions.values():
            self.non_terminals.add(lhs)

        self.non_terminals.add(self.augmented_start_symbol)

        for _, rhs_list in self.productions.values():
            for symbol in rhs_list:
                if symbol not in self.non_terminals and symbol != EPSILON:
                    self.terminals.add(symbol)

        # 确保推断出的起始符号确实是非终结符
        if self.start_symbol not in self.non_terminals and self.start_symbol is not None:
            # This might indicate an issue if start_symbol was accidentally a terminal
            # or if grammar parsing had an issue. For now, we assume it's correctly a non-terminal.
            # print(f"Warning: Inferred start symbol '{self.start_symbol}' is not in the set of non-terminals.")
            pass

    def get_production_by_id(self, prod_id):
        return self.productions[prod_id]

    def get_production_id(self, lhs, rhs):
        # Ensure rhs is a list for comparison
        rhs_list = list(rhs) if not isinstance(rhs, list) else rhs
        for idx, (l, r_stored) in self.productions.items():
            if l == lhs and r_stored == rhs_list:
                return idx
        return None

    def pretty_print_productions(self):
        print("--- Productions ---")
        for idx, (lhs, rhs) in self.productions.items():
            print(f"{idx}: {lhs} → {' '.join(rhs)}")
        print(f"Terminals: {sorted(list(self.terminals))}")
        print(f"Non-Terminals: {sorted(list(self.non_terminals))}")
        print(f"Start Symbol (original): {self.start_symbol}")
        print(f"Augmented Start Symbol: {self.augmented_start_symbol}")


class SLRParser:
    def __init__(self, grammar: Grammar):
        self.grammar = grammar
        self.EPSILON = EPSILON
        self.DOLLAR = DOLLAR
        print("计算FIRST集...")
        self._compute_first_sets()
        print("计算FOLLOW集...")
        self._compute_follow_sets()
        print("构建LR(0)项集规范族 (状态)...")
        self._build_canonical_collection()
        print("构建SLR(1)分析表...")
        self._build_parsing_table()
        print("SLR Parser初始化完成。")

    def _compute_first_sets(self):
        """ 计算所有非终结符的FIRST集 """
        first = self.grammar.first_sets
        for t in self.grammar.terminals:
            first[t] = {t}
        first[self.EPSILON] = {self.EPSILON}
        for nt in self.grammar.non_terminals:
            first[nt] = set()

        changed = True
        while changed:
            changed = False
            for _, (lhs, rhs) in self.grammar.productions.items():
                original_first_lhs_size = len(first[lhs])
                can_derive_epsilon = True
                for symbol_in_rhs in rhs:
                    # Add FIRST(symbol_in_rhs) - {EPSILON} to FIRST(lhs)
                    for s_first in first[symbol_in_rhs]:
                        if s_first != self.EPSILON:
                            first[lhs].add(s_first)

                    if self.EPSILON not in first[symbol_in_rhs]:
                        can_derive_epsilon = False
                        break

                if can_derive_epsilon:  # All symbols in RHS can derive EPSILON
                    first[lhs].add(self.EPSILON)

                if len(first[lhs]) > original_first_lhs_size:
                    changed = True

    def _get_first_for_sequence(self, sequence):
        """ 计算符号序列的FIRST集 """
        result = set()
        all_can_be_epsilon = True
        for symbol in sequence:
            symbol_first = self.grammar.first_sets[symbol]
            result.update(s for s in symbol_first if s != self.EPSILON)
            if self.EPSILON not in symbol_first:
                all_can_be_epsilon = False
                break
        if all_can_be_epsilon:
            result.add(self.EPSILON)
        return result

    def _compute_follow_sets(self):
        """ 计算所有非终结符的FOLLOW集 """
        follow = self.grammar.follow_sets
        for nt in self.grammar.non_terminals:
            follow[nt] = set()

        follow[self.grammar.augmented_start_symbol] = {self.DOLLAR}

        changed = True
        while changed:
            changed = False
            for _, (lhs, rhs) in self.grammar.productions.items():
                for i, symbol_B in enumerate(rhs):
                    if symbol_B in self.grammar.non_terminals:
                        original_follow_B_size = len(follow[symbol_B])
                        beta = rhs[i + 1:]

                        if beta:
                            first_beta = self._get_first_for_sequence(beta)
                            for s in first_beta:
                                if s != self.EPSILON:
                                    follow[symbol_B].add(s)
                            if self.EPSILON in first_beta:
                                follow[symbol_B].update(follow[lhs])
                        else:
                            follow[symbol_B].update(follow[lhs])

                        if len(follow[symbol_B]) > original_follow_B_size:
                            changed = True

    def _closure(self, item_set):
        """ 计算LR(0)项集的闭包 """
        closure_set = set(item_set)
        worklist = list(item_set)

        while worklist:
            item_lhs, item_rhs_tuple, dot_pos = worklist.pop(0)

            if dot_pos < len(item_rhs_tuple) and item_rhs_tuple[
                dot_pos] != self.EPSILON:  # EPSILON has no productions from it
                symbol_after_dot = item_rhs_tuple[dot_pos]
                if symbol_after_dot in self.grammar.non_terminals:
                    for prod_idx, (prod_lhs, prod_rhs) in self.grammar.productions.items():
                        if prod_lhs == symbol_after_dot:
                            rhs_for_item = tuple(prod_rhs)
                            # For B -> . ε, item_rhs_tuple will be (EPSILON,), dot_pos=0
                            # For B -> γ (γ != ε), item is (B, tuple(γ), 0)
                            new_item = (prod_lhs, rhs_for_item, 0)

                            if new_item not in closure_set:
                                closure_set.add(new_item)
                                worklist.append(new_item)
        return frozenset(closure_set)

    def _goto(self, item_set, symbol):
        """ 计算从项集I通过符号X到达的项集 """
        next_items = set()
        for item_lhs, item_rhs_tuple, dot_pos in item_set:
            if dot_pos < len(item_rhs_tuple) and item_rhs_tuple[dot_pos] == symbol:
                next_items.add((item_lhs, item_rhs_tuple, dot_pos + 1))
        return self._closure(next_items)

    def _build_canonical_collection(self):
        """ 构建LR(0)项集的规范族 (状态) """
        start_prod_lhs, start_prod_rhs = self.grammar.productions[0]  # S' -> S
        initial_item = (start_prod_lhs, tuple(start_prod_rhs), 0)  # S' -> .S

        initial_state_items = self._closure({initial_item})

        self.grammar.lr0_items_sets = [initial_state_items]
        item_set_to_idx = {initial_state_items: 0}

        worklist = [0]
        idx_counter = 1

        while worklist:
            current_state_idx = worklist.pop(0)
            current_item_set = self.grammar.lr0_items_sets[current_state_idx]
            all_symbols = self.grammar.terminals.union(self.grammar.non_terminals)

            for symbol in all_symbols:
                if symbol == self.EPSILON: continue

                next_item_set = self._goto(current_item_set, symbol)
                if not next_item_set:
                    continue

                if next_item_set not in item_set_to_idx:
                    self.grammar.lr0_items_sets.append(next_item_set)
                    new_idx = idx_counter
                    item_set_to_idx[next_item_set] = new_idx
                    self.grammar.goto_map[(current_state_idx, symbol)] = new_idx
                    worklist.append(new_idx)
                    idx_counter += 1
                else:
                    self.grammar.goto_map[(current_state_idx, symbol)] = item_set_to_idx[next_item_set]

        # print(f"生成了 {len(self.grammar.lr0_items_sets)} 个状态。")

    def _build_parsing_table(self):
        """ 构建SLR(1)分析表 (ACTION and GOTO tables) """
        action_tbl = self.grammar.action_table
        goto_tbl = self.grammar.goto_table
        num_conflicts = 0

        for i, item_set in enumerate(self.grammar.lr0_items_sets):
            for item_lhs, item_rhs_tuple, dot_pos in item_set:
                # Case 1: Shift Action: [A -> α . a β], 'a' is terminal
                if dot_pos < len(item_rhs_tuple) and item_rhs_tuple[dot_pos] != self.EPSILON:
                    symbol_after_dot = item_rhs_tuple[dot_pos]
                    if symbol_after_dot in self.grammar.terminals:
                        target_state_idx = self.grammar.goto_map.get((i, symbol_after_dot))
                        if target_state_idx is not None:
                            action_key = (i, symbol_after_dot)
                            new_action = ('shift', target_state_idx)
                            if action_key in action_tbl and action_tbl[action_key] != new_action:
                                print(
                                    f"冲突警告! 状态 {i}, 终结符 {symbol_after_dot}: 已存在 {action_tbl[action_key]}, 新建 {new_action} (Shift)")
                                num_conflicts += 1
                                # 默认解决S/R冲突：倾向于shift (常见策略)
                                if action_tbl[action_key][0] == 'reduce':
                                    print("  Shift/Reduce 冲突 - 倾向于 Shift.")
                                    action_tbl[action_key] = new_action  # Prefer shift
                                else:
                                    print("  Shift/Shift 冲突? (不应发生)")
                            else:
                                action_tbl[action_key] = new_action

                # Case 2: Reduce Action: [A -> α .], A != S'
                # (Handle A -> ε . as well, where item_rhs_tuple is (EPSILON,) and dot_pos is 0 or 1)
                is_epsilon_production_at_end = item_rhs_tuple == (
                self.EPSILON,) and dot_pos == 1  # A -> ε . (dot after epsilon)
                is_normal_production_at_end = dot_pos == len(item_rhs_tuple) and item_rhs_tuple != (self.EPSILON,)

                if is_normal_production_at_end or is_epsilon_production_at_end:
                    if item_lhs == self.grammar.augmented_start_symbol:  # S' -> S .
                        # Case 3: Accept Action
                        if item_rhs_tuple != (self.EPSILON,):  # Ensure it's not S' -> . S
                            action_tbl[(i, self.DOLLAR)] = ('accept',)
                    else:  # A -> α . (A != S')
                        # For A -> ε, rhs is [EPSILON]. The item may be (A, (EPSILON,), 1)
                        # or (A, (EPSILON,), 0) if we consider dot before. Closure usually adds (A, (EPSILON,),0)
                        # and goto on EPSILON might not be defined.
                        # Let's assume reduce items A -> alpha . means dot is at the very end.
                        # If item_rhs_tuple is (EPSILON,), it means A -> epsilon.
                        actual_rhs = [self.EPSILON] if item_rhs_tuple == (self.EPSILON,) else list(item_rhs_tuple)
                        prod_id = self.grammar.get_production_id(item_lhs, actual_rhs)

                        if prod_id is None:
                            # This can happen if an item like (X, (EPSILON,), 1) is formed.
                            # Check if it's (X, (EPSILON,),0) meaning X -> .ε and we need to reduce X->ε.
                            if item_rhs_tuple == (self.EPSILON,):  # It's an epsilon production
                                prod_id = self.grammar.get_production_id(item_lhs, [self.EPSILON])

                        if prod_id is not None:
                            for terminal in self.grammar.follow_sets[item_lhs]:
                                action_key = (i, terminal)
                                new_action = ('reduce', prod_id)
                                if action_key in action_tbl and action_tbl[action_key] != new_action:
                                    existing_action = action_tbl[action_key]
                                    print(f"冲突警告! 状态 {i}, 终结符 {terminal} (FOLLOW of {item_lhs}): "
                                          f"已存在 {existing_action}, 新建 {new_action} (Reduce by {prod_id}: {item_lhs} -> {' '.join(actual_rhs)})")
                                    num_conflicts += 1
                                    if existing_action[0] == 'shift':
                                        print(f"  Shift/Reduce 冲突 - 当前规则: 倾向于 Shift.")
                                        # Keep existing shift, or implement precedence
                                    elif existing_action[0] == 'reduce':
                                        print(
                                            f"  Reduce/Reduce 冲突! 文法可能不是 SLR(1). 保留现有规约 {existing_action[1]}.")
                                        # Keep existing reduce or raise error
                                else:
                                    action_tbl[action_key] = new_action
                        # else:
                        # print(f"警告: 无法找到产生式ID进行规约: {item_lhs} -> {item_rhs_tuple}")

            # Fill GOTO table (for non-terminals)
            for non_terminal in self.grammar.non_terminals:
                if non_terminal == self.grammar.augmented_start_symbol: continue  # No GOTO on S'
                target_state_idx = self.grammar.goto_map.get((i, non_terminal))
                if target_state_idx is not None:
                    goto_tbl[(i, non_terminal)] = target_state_idx

        if num_conflicts > 0:
            print(f"构建分析表时发现 {num_conflicts} 个冲突。请检查文法。")

    def print_parsing_table(self):
        """ 打印生成的ACTION和GOTO表 """
        if not self.grammar.lr0_items_sets:
            print("没有生成状态，无法打印分析表。")
            return

        print("\n--- SLR(1) Parsing Table ---")
        terminals_sorted = sorted(list(self.grammar.terminals - {self.EPSILON}))
        non_terminals_sorted = sorted(list(self.grammar.non_terminals - {self.grammar.augmented_start_symbol}))

        header = terminals_sorted + [self.DOLLAR] + non_terminals_sorted

        max_cell_width = 0
        # Pre-calculate max_cell_width
        temp_table_repr = {}
        for r_idx in range(len(self.grammar.lr0_items_sets)):
            temp_table_repr[r_idx] = {}
            for h_sym in header:
                cell_val = ""
                if h_sym in self.grammar.terminals or h_sym == self.DOLLAR:
                    action = self.grammar.action_table.get((r_idx, h_sym))
                    if action:
                        if action[0] == 'shift':
                            cell_val = f"s{action[1]}"
                        elif action[0] == 'reduce':
                            cell_val = f"r{action[1]}"
                        elif action[0] == 'accept':
                            cell_val = "acc"
                else:  # Non-terminal for GOTO
                    goto_state = self.grammar.goto_table.get((r_idx, h_sym))
                    if goto_state is not None: cell_val = str(goto_state)
                temp_table_repr[r_idx][h_sym] = cell_val
                max_cell_width = max(max_cell_width, len(cell_val))

        max_cell_width = max(max_cell_width, max(len(h) for h in header if h) if header else 4)
        max_cell_width = max(max_cell_width, len("State"))  # Ensure "State" fits
        max_cell_width += 1  # Padding

        # Header row
        print(f"{'State':<{max_cell_width}}|" + "".join([f"{h:<{max_cell_width}}|" for h in header]))
        # Separator line
        print("-" * (max_cell_width + 1 + len(header) * (max_cell_width + 1)))

        for i in range(len(self.grammar.lr0_items_sets)):
            row_str = f"{i:<{max_cell_width}}|"
            for sym in header:
                cell = temp_table_repr[i][sym]
                row_str += f"{cell:<{max_cell_width}}|"
            print(row_str)
        print("-" * (max_cell_width + 1 + len(header) * (max_cell_width + 1)))

    def parse(self, token_stream, error_file="error_log.txt"):
        """ 执行SLR(1)分析过程 """
        tokens = list(token_stream)
        if not tokens or tokens[-1] != self.DOLLAR:
            tokens.append(self.DOLLAR)

        state_stack = [0]
        symbol_stack = [self.DOLLAR]

        print(f"{'Stack':<25} | {'Symbols':<30} | {'Input':<30} | {'Action'}")
        print("-" * 120)

        ip = 0
        error_occurred = False
        log_output = []

        def log_step(state_s, symbol_s, input_s, action_s):
            log_output.append(f"{state_s:<25} | {symbol_s:<30} | {input_s:<30} | {action_s}")

        while True:
            if not state_stack:  # Should not happen in correct execution
                print("错误: 状态栈为空!")
                error_occurred = True
                break
            current_state = state_stack[-1]

            if ip >= len(tokens):  # Should be caught by accept or error before this
                print("错误: 输入指针超出范围，但解析未结束。")
                error_occurred = True
                break
            current_input_symbol = tokens[ip]

            stack_str = " ".join(map(str, state_stack))
            symbol_str = " ".join(symbol_stack)
            input_str = " ".join(tokens[ip:])

            action_entry = self.grammar.action_table.get((current_state, current_input_symbol))

            if action_entry is None:
                action_str = f"错误: 状态 {current_state}, 符号 {current_input_symbol} 无对应动作"
                log_step(stack_str, symbol_str, input_str, action_str)
                error_occurred = True
                with open(error_file, "a", encoding="utf-8") as ef:
                    ef.write(f"语法错误: 在状态 {current_state} 遇到非预期符号 '{current_input_symbol}'.\n")
                    ef.write(f"  Stack: {stack_str}\n  Symbols: {symbol_str}\n  Remaining Input: {input_str}\n")
                break

            action_type = action_entry[0]

            if action_type == 'shift':
                target_state = action_entry[1]
                action_str = f"Shift to {target_state}"
                log_step(stack_str, symbol_str, input_str, action_str)

                state_stack.append(target_state)
                symbol_stack.append(current_input_symbol)
                ip += 1

            elif action_type == 'reduce':
                prod_id = action_entry[1]
                lhs, rhs_list = self.grammar.get_production_by_id(prod_id)
                action_str = f"Reduce by {lhs} → {' '.join(rhs_list)}"
                log_step(stack_str, symbol_str, input_str, action_str)

                num_to_pop = 0
                if rhs_list != [self.EPSILON]:
                    num_to_pop = len(rhs_list)

                for _ in range(num_to_pop):
                    if state_stack: state_stack.pop()
                    if symbol_stack: symbol_stack.pop()

                if not state_stack:
                    print("错误: 状态栈在Reduce操作后为空!")
                    error_occurred = True;
                    break
                s_prime = state_stack[-1]
                symbol_stack.append(lhs)

                goto_dest = self.grammar.goto_table.get((s_prime, lhs))
                if goto_dest is None:
                    print(f"错误: GOTO表中无对应项: GOTO({s_prime}, {lhs})")
                    error_occurred = True;
                    break
                state_stack.append(goto_dest)

            elif action_type == 'accept':
                action_str = "Accept"
                log_step(stack_str, symbol_str, input_str, action_str)
                print("\n解析成功!")
                break
            else:
                action_str = f"错误: 未知动作 {action_entry}"
                log_step(stack_str, symbol_str, input_str, action_str)
                error_occurred = True
                with open(error_file, "a", encoding="utf-8") as ef:
                    ef.write(f"内部解析器错误: 未知动作 {action_entry}\n")
                break

        for line in log_output:
            print(line)

        if error_occurred:
            print(f"\n解析失败。错误详情请查看 '{error_file}'。")
        return not error_occurred


# --- 新语言的词法分析器 ---
def tokenize_new_lang(code_string):
    """为新的类C/Pascal语言设计的词法分析器"""
    token_specification = [
        # Keywords (顺序很重要，特别是当一个是另一个的前缀时，这里不是问题)
        ('INT_KW', r'\bint\b'),
        ('FLOAT_KW', r'\bfloat\b'),
        ('IF_KW', r'\bif\b'),
        ('ELSE_KW', r'\belse\b'),
        ('WHILE_KW', r'\bwhile\b'),
        # Literals (FLOATLITERAL应在INTLITERAL之前，以正确处理如 "1." 或 ".5")
        ('FLOATLITERAL', r'\d+\.\d*([Ee][+-]?\d+)?|\d*\.\d+([Ee][+-]?\d+)?'),
        ('INTLITERAL', r'\d+'),
        # Identifiers (在关键字之后，以避免如 "int" 被识别为ID)
        ('ID', r'[a-zA-Z_][a-zA-Z0-9_]*'),
        # Operators (多字符运算符应在单字符运算符之前)
        ('ASSIGN_PASCAL', r':='),
        ('GE_KW', r'>='),  # Greater than or equal
        # ('LE_KW',        r'<='), # Less than or equal (if needed)
        # ('EQ_KW',        r'=='), # Equal (if needed)
        # ('NE_KW',        r'!='), # Not equal (if needed)
        ('ASSIGN', r'='),  # Assignment (after multi-char ops like :=, ==)
        ('GT_KW', r'>'),  # Greater than (after >=)
        # ('LT_KW',        r'<'),  # Less than (after <=) (if needed)
        ('PLUS', r'\+'),
        ('MINUS', r'-'),
        ('TIMES', r'\*'),
        ('DIVIDE', r'/'),
        # Punctuation
        ('LPAREN', r'\('), ('RPAREN', r'\)'),
        ('LBRACE', r'\{'), ('RBRACE', r'\}'),
        ('LBRACKET', r'\['), ('RBRACKET', r'\]'),
        ('SEMI', r';'), ('COMMA', r','),

        ('SKIP', r'[ \t\n]+'),  # 跳过空白符
        ('LINE_COMMENT', r'//.*'),  # 行注释
        ('MISMATCH', r'.'),  # 匹配任何其他字符 (错误)
    ]

    tok_regex = '|'.join('(?P<%s>%s)' % pair for pair in token_specification)
    tokens = []
    line_num = 1
    line_start = 0
    for mo in re.finditer(tok_regex, code_string):
        kind = mo.lastgroup
        value = mo.group()
        column = mo.start() - line_start

        if kind == 'SKIP' or kind == 'LINE_COMMENT':
            if '\n' in value:
                line_num += value.count('\n')
                line_start = mo.end() - value.rfind('\n') - 1
            continue
        elif kind == 'MISMATCH':
            print(f"词法错误 (L{line_num} C{column}): 非预期字符 '{value}'")

            raise ValueError(f"词法错误 (L{line_num} C{column}): 非预期字符 '{value}'")

        tokens.append(kind)
    return tokens


# --- 新语言的文法规则 ---
# 重要提示：这个文法是一个初步的草案，用于尝试解析您提供的示例。

new_grammar_rules_str = """
program -> declaration_list_opt statement_list_opt

declaration_list_opt -> declaration_list | ε
declaration_list -> declaration | declaration declaration_list

declaration -> type_specifier init_declarator_list SEMI
type_specifier -> INT_KW | FLOAT_KW

init_declarator_list -> init_declarator | init_declarator COMMA init_declarator_list
init_declarator -> declarator | declarator ASSIGN additive_expr  // 允许简单初始化，如 k=18

declarator -> ID
declarator -> ID LBRACKET INTLITERAL RBRACKET
declarator -> ID LBRACKET INTLITERAL RBRACKET LBRACKET INTLITERAL RBRACKET

statement_list_opt -> statement_list | ε
statement_list -> statement | statement statement_list

statement -> compound_stmt | selection_stmt | iteration_stmt | expression_stmt | SEMI // SEMI for empty statement

compound_stmt -> LBRACE declaration_list_opt statement_list_opt RBRACE

expression_stmt -> expression SEMI

expression -> assignment_expr
assignment_expr -> conditional_expr | lvalue assignment_operator assignment_expr // 右结合赋值 m=n=1
lvalue -> ID
lvalue -> ID LBRACKET expression RBRACKET
lvalue -> ID LBRACKET expression RBRACKET LBRACKET expression RBRACKET
assignment_operator -> ASSIGN | ASSIGN_PASCAL

conditional_expr -> logical_OR_expr // 示例中没有三元运算符或复杂逻辑

logical_OR_expr -> logical_AND_expr // 示例中没有 OR
logical_AND_expr -> equality_expr   // 示例中没有 AND
equality_expr -> relational_expr    // 示例中没有 ==, !=

relational_expr -> additive_expr | additive_expr GT_KW additive_expr | additive_expr GE_KW additive_expr
# Add LT_KW, LE_KW if needed: | additive_expr LT_KW additive_expr | additive_expr LE_KW additive_expr

additive_expr -> multiplicative_expr | additive_expr PLUS multiplicative_expr | additive_expr MINUS multiplicative_expr
multiplicative_expr -> primary_expr | multiplicative_expr TIMES primary_expr | multiplicative_expr DIVIDE primary_expr
# Removed unary_expr for simplification as per example structure (k+9*5, not -k etc.)
# If unary ops are needed: multiplicative_expr -> unary_expr | ... ; unary_expr -> primary_expr | PLUS unary_expr ...

primary_expr -> ID
primary_expr -> INTLITERAL
primary_expr -> FLOATLITERAL
primary_expr -> LPAREN expression RPAREN
primary_expr -> lvalue  // 允许数组访问作为表达式的一部分

selection_stmt -> IF_KW LPAREN expression RPAREN statement else_clause
else_clause -> ELSE_KW statement | ε

iteration_stmt -> WHILE_KW LPAREN expression RPAREN statement
"""

# --- Main Execution ---
if __name__ == "__main__":
    # 示例输入代码
    input_code_complex = """
    int a[3][3], b[10][15], k,m,n;
    float i;

    k:=2;
    m=n=1;
    if(m>n) a[m][n]=k+9*5; else m=0;

    while(m>=0){
        float k=18; 
        int a[5][8]; 
        a[m][n]=k;
        m = m - 1; 
    }
    a[m][n]=k;
    """

    print("--- 解析复杂语言示例 ---")
    print("输入代码:")
    print(input_code_complex)

    error_log_file = "complex_lang_errors.txt"
    # 清空之前的错误日志
    with open(error_log_file, "w", encoding="utf-8") as f:
        f.write("")

    try:
        print("\n词法分析...")
        tokens = tokenize_new_lang(input_code_complex)
        print(f"Tokens: {tokens}")

        print("\n创建文法和解析器...")
        # 'program' 是新文法的起始符号
        grammar_complex = Grammar(new_grammar_rules_str, explicit_start_symbol="program")
        grammar_complex.pretty_print_productions()  # 打印产生式看看

        parser_complex = SLRParser(grammar_complex)
        # parser_complex.print_parsing_table() # 表可能非常大

        print("\n开始语法分析...")
        parser_complex.parse(tokens, error_file=error_log_file)

    except ValueError as ve:
        print(f"发生错误: {ve}")
    except Exception as e:
        print(f"发生意外错误: {e}")
        import traceback

        traceback.print_exc()

