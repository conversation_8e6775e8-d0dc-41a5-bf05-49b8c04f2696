# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['manga_converter_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('D:\\Work__\\Python_Version\\tcl\\tcl8.6', 'tcl/tcl8.6'), ('D:\\Work__\\Python_Version\\tcl\\tk8.6', 'tk/tk8.6')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MangaPDFConverter',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['favicon.ico'],
)
