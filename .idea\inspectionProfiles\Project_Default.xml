<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="73">
            <item index="0" class="java.lang.String" itemvalue="google-pasta" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="python-lsp-server" />
            <item index="3" class="java.lang.String" itemvalue="tensorflow-estimator" />
            <item index="4" class="java.lang.String" itemvalue="tabulate" />
            <item index="5" class="java.lang.String" itemvalue="PyYAML" />
            <item index="6" class="java.lang.String" itemvalue="atomicwrites" />
            <item index="7" class="java.lang.String" itemvalue="constantly" />
            <item index="8" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="9" class="java.lang.String" itemvalue="patsy" />
            <item index="10" class="java.lang.String" itemvalue="pyls-spyder" />
            <item index="11" class="java.lang.String" itemvalue="tables" />
            <item index="12" class="java.lang.String" itemvalue="TBB" />
            <item index="13" class="java.lang.String" itemvalue="mccabe" />
            <item index="14" class="java.lang.String" itemvalue="certifi" />
            <item index="15" class="java.lang.String" itemvalue="PyDispatcher" />
            <item index="16" class="java.lang.String" itemvalue="anaconda-navigator" />
            <item index="17" class="java.lang.String" itemvalue="bkcharts" />
            <item index="18" class="java.lang.String" itemvalue="comtypes" />
            <item index="19" class="java.lang.String" itemvalue="pywin32" />
            <item index="20" class="java.lang.String" itemvalue="clyent" />
            <item index="21" class="java.lang.String" itemvalue="navigator-updater" />
            <item index="22" class="java.lang.String" itemvalue="wincertstore" />
            <item index="23" class="java.lang.String" itemvalue="xlwings" />
            <item index="24" class="java.lang.String" itemvalue="win-unicode-console" />
            <item index="25" class="java.lang.String" itemvalue="h5py" />
            <item index="26" class="java.lang.String" itemvalue="wrapt" />
            <item index="27" class="java.lang.String" itemvalue="daal4py" />
            <item index="28" class="java.lang.String" itemvalue="fonttools" />
            <item index="29" class="java.lang.String" itemvalue="tensorboard" />
            <item index="30" class="java.lang.String" itemvalue="cssselect" />
            <item index="31" class="java.lang.String" itemvalue="cytoolz" />
            <item index="32" class="java.lang.String" itemvalue="multipledispatch" />
            <item index="33" class="java.lang.String" itemvalue="dask" />
            <item index="34" class="java.lang.String" itemvalue="mkl-service" />
            <item index="35" class="java.lang.String" itemvalue="pathspec" />
            <item index="36" class="java.lang.String" itemvalue="brotlipy" />
            <item index="37" class="java.lang.String" itemvalue="pycurl" />
            <item index="38" class="java.lang.String" itemvalue="queuelib" />
            <item index="39" class="java.lang.String" itemvalue="scikit-learn-intelex" />
            <item index="40" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="41" class="java.lang.String" itemvalue="pep8" />
            <item index="42" class="java.lang.String" itemvalue="pycosat" />
            <item index="43" class="java.lang.String" itemvalue="asynctest" />
            <item index="44" class="java.lang.String" itemvalue="llvmlite" />
            <item index="45" class="java.lang.String" itemvalue="pycrypto" />
            <item index="46" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="47" class="java.lang.String" itemvalue="alabaster" />
            <item index="48" class="java.lang.String" itemvalue="jupyter" />
            <item index="49" class="java.lang.String" itemvalue="conda-repo-cli" />
            <item index="50" class="java.lang.String" itemvalue="backports.weakref" />
            <item index="51" class="java.lang.String" itemvalue="conda" />
            <item index="52" class="java.lang.String" itemvalue="zict" />
            <item index="53" class="java.lang.String" itemvalue="sip" />
            <item index="54" class="java.lang.String" itemvalue="datashape" />
            <item index="55" class="java.lang.String" itemvalue="pyreadline" />
            <item index="56" class="java.lang.String" itemvalue="python-lsp-jsonrpc" />
            <item index="57" class="java.lang.String" itemvalue="pytest" />
            <item index="58" class="java.lang.String" itemvalue="black" />
            <item index="59" class="java.lang.String" itemvalue="conda-build" />
            <item index="60" class="java.lang.String" itemvalue="Keras-Applications" />
            <item index="61" class="java.lang.String" itemvalue="munkres" />
            <item index="62" class="java.lang.String" itemvalue="future" />
            <item index="63" class="java.lang.String" itemvalue="mpmath" />
            <item index="64" class="java.lang.String" itemvalue="statsmodels" />
            <item index="65" class="java.lang.String" itemvalue="olefile" />
            <item index="66" class="java.lang.String" itemvalue="pytz" />
            <item index="67" class="java.lang.String" itemvalue="webencodings" />
            <item index="68" class="java.lang.String" itemvalue="inflection" />
            <item index="69" class="java.lang.String" itemvalue="Pillow" />
            <item index="70" class="java.lang.String" itemvalue="Flask-Session" />
            <item index="71" class="java.lang.String" itemvalue="Flask" />
            <item index="72" class="java.lang.String" itemvalue="scapy" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="任务提交.main.*" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>