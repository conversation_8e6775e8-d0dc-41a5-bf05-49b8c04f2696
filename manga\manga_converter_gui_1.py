import tkinter as tk
from tkinter import filedialog, scrolledtext, messagebox, ttk
import os
import re
from PIL import Image
import threading


# --- Core PDF Creation Logic (adapted from previous script) ---
def create_manga_pdf_core(image_folder, output_filename, resolution, log_callback):
    actual_output_pdf_path = os.path.join(image_folder, output_filename)

    if os.path.exists(actual_output_pdf_path):
        log_callback(f"  PDF已存在，跳过: {output_filename} 在 {os.path.basename(image_folder)}")
        return "skipped"

    image_files_in_this_folder = []
    supported_formats = ('.jpg', '.jpeg', '.png')
    try:
        for f_name in os.listdir(image_folder):
            if f_name.lower().endswith(supported_formats):
                image_files_in_this_folder.append(os.path.join(image_folder, f_name))
    except Exception as e:
        log_callback(f"  错误: 列出 '{os.path.basename(image_folder)}' 文件时出错: {e}")
        return "failed"

    if not image_files_in_this_folder:
        log_callback(f"  信息: '{os.path.basename(image_folder)}' 中无图片文件。")
        return "no_images"

    def sort_key(filepath):
        filename = os.path.basename(filepath).lower()
        if filename == "cover.jpg": return (-1, "")
        match = re.match(r'i_(\d+)\.(jpg|jpeg|png)', filename)
        if match: return (0, int(match.group(1)))
        return (1, filename)

    image_files_in_this_folder.sort(key=sort_key)

    pil_images = []
    opened_images_refs = []

    try:
        log_callback(f"  为 '{os.path.basename(image_folder)}' 处理图片 (共 {len(image_files_in_this_folder)} 页)...")
        for i, f_path in enumerate(image_files_in_this_folder):
            # log_callback(f"    处理中: {os.path.basename(f_path)} ({i+1}/{len(image_files_in_this_folder)})") # Too verbose for GUI
            img = Image.open(f_path)
            opened_images_refs.append(img)

            if img.mode == 'RGBA':
                background = Image.new("RGB", img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[3])
                img_to_add = background
            elif img.mode == 'P':
                img_to_add = img.convert('RGB')
            elif img.mode == 'L':
                img_to_add = img
            elif img.mode == 'RGB':
                img_to_add = img
            else:
                img_to_add = img.convert('RGB')

            pil_images.append(img_to_add)

    except Exception as e:
        log_callback(f"  错误: 在 '{os.path.basename(image_folder)}' 中打开/转换图片时出错: {e}")
        for opened_img in opened_images_refs: opened_img.close()
        return "failed"

    if not pil_images:
        log_callback(f"  信息: 没有有效图片添加到 '{os.path.basename(image_folder)}' 的PDF。")
        for opened_img in opened_images_refs: opened_img.close()
        return "no_images"

    first_image_to_save = pil_images[0]
    other_images_to_save = pil_images[1:]

    try:
        log_callback(f"  保存PDF: {output_filename} (在 {os.path.basename(image_folder)})")
        first_image_to_save.save(
            actual_output_pdf_path, "PDF", resolution=resolution,
            save_all=True, append_images=other_images_to_save
        )
        log_callback(f"  PDF创建成功: {output_filename} 在 {os.path.basename(image_folder)}")
        return "success"
    except Exception as e:
        log_callback(f"  错误: 保存PDF '{output_filename}' 时出错: {e}")
        return "failed"
    finally:
        for opened_img in opened_images_refs:
            opened_img.close()


def process_directory_recursively_core(root_dir, resolution, log_callback, progress_callback):
    log_callback(f"开始递归扫描根目录: {root_dir}")
    counts = {"success": 0, "skipped": 0, "no_images": 0, "failed": 0, "total_image_folders": 0}

    image_folders_to_process = []
    for dirpath, _, filenames in os.walk(root_dir):
        if any(fname.lower().endswith(('.jpg', '.jpeg', '.png')) for fname in filenames):
            image_folders_to_process.append(dirpath)
            counts["total_image_folders"] += 1

    if not image_folders_to_process:
        log_callback("在所选目录及其子目录中未找到包含图片的文件夹。")
        return counts

    log_callback(f"共找到 {counts['total_image_folders']} 个包含图片的文件夹准备处理。")
    progress_callback(0, counts["total_image_folders"])  # Initialize progress

    for i, dirpath in enumerate(image_folders_to_process):
        log_callback(f"\n处理文件夹 ({i + 1}/{counts['total_image_folders']}): {dirpath}")

        folder_basename = os.path.basename(dirpath)
        output_pdf_name = f"{folder_basename}.pdf"
        output_pdf_name = re.sub(r'[\\/*?:"<>|]', "_", output_pdf_name)

        result = create_manga_pdf_core(dirpath, output_pdf_name, resolution, log_callback)
        counts[result] = counts.get(result, 0) + 1
        progress_callback(i + 1, counts["total_image_folders"])

    log_callback(f"\n--- 扫描完成 ---")
    log_callback(f"成功创建: {counts['success']}")
    log_callback(f"已存在并跳过: {counts['skipped']}")
    log_callback(f"因无图片跳过: {counts['no_images']}")  # Technically create_manga_pdf_core handles this
    log_callback(f"创建失败: {counts['failed']}")
    return counts


# --- Tkinter GUI Application ---
class MangaConverterApp:
    def __init__(self, root):
        self.root = root
        root.title("漫画文件夹转PDF转换器")
        root.geometry("700x500")  # Increased height for progress bar

        # Frame for directory selection
        dir_frame = ttk.LabelFrame(root, text="选择根目录", padding=(10, 5))
        dir_frame.pack(padx=10, pady=5, fill="x")

        self.dir_path_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.dir_path_var, width=70).pack(side=tk.LEFT, padx=(0, 5), expand=True,
                                                                            fill="x")
        ttk.Button(dir_frame, text="浏览...", command=self.browse_directory).pack(side=tk.LEFT)

        # Frame for settings
        settings_frame = ttk.LabelFrame(root, text="设置", padding=(10, 5))
        settings_frame.pack(padx=10, pady=5, fill="x")

        ttk.Label(settings_frame, text="PDF分辨率 (DPI):").pack(side=tk.LEFT, padx=(0, 5))
        self.resolution_var = tk.StringVar(value="150")
        ttk.Entry(settings_frame, textvariable=self.resolution_var, width=10).pack(side=tk.LEFT)

        # Frame for actions
        action_frame = ttk.Frame(root, padding=(10, 5))
        action_frame.pack(padx=10, pady=5, fill="x")

        self.start_button = ttk.Button(action_frame, text="开始转换", command=self.start_conversion_thread)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_log_button = ttk.Button(action_frame, text="清空日志", command=self.clear_log)
        self.clear_log_button.pack(side=tk.LEFT)

        # Progress Bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(root, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(padx=10, pady=5, fill="x")

        # Log area
        log_frame = ttk.LabelFrame(root, text="日志", padding=(10, 5))
        log_frame.pack(padx=10, pady=10, fill="both", expand=True)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15, state=tk.DISABLED)
        self.log_text.pack(fill="both", expand=True)

    def browse_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.dir_path_var.set(directory)
            self.log_message(f"选择目录: {directory}")

    def log_message(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)  # Auto-scroll
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()  # Ensure GUI updates immediately

    def update_progress(self, current, total):
        if total > 0:
            percentage = (current / total) * 100
            self.progress_var.set(percentage)
        else:
            self.progress_var.set(0)
        self.root.update_idletasks()

    def clear_log(self):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.progress_var.set(0)

    def start_conversion_thread(self):
        root_dir = self.dir_path_var.get()
        if not root_dir:
            messagebox.showerror("错误", "请先选择一个根目录。")
            return
        if not os.path.isdir(root_dir):
            messagebox.showerror("错误", "选择的路径不是一个有效的目录。")
            return

        try:
            resolution = float(self.resolution_var.get())
            if resolution <= 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("错误", "请输入一个有效的正数作为分辨率。")
            return

        self.start_button.config(state=tk.DISABLED)
        self.clear_log_button.config(state=tk.DISABLED)  # Disable while processing
        self.progress_var.set(0)  # Reset progress bar

        # Run the core logic in a separate thread to keep GUI responsive
        thread = threading.Thread(target=self.run_conversion_logic, args=(root_dir, resolution))
        thread.daemon = True  # Allows main program to exit even if thread is running
        thread.start()

    def run_conversion_logic(self, root_dir, resolution):
        try:
            self.log_message("转换开始...")
            process_directory_recursively_core(root_dir, resolution, self.log_message, self.update_progress)
            self.log_message("转换完成！")
            messagebox.showinfo("完成", "所有文件夹处理完毕！")
        except Exception as e:
            self.log_message(f"发生严重错误: {e}")
            messagebox.showerror("严重错误", f"处理过程中发生错误: {e}")
        finally:
            # Ensure buttons are re-enabled in the main thread
            self.root.after(0, self.enable_buttons)

    def enable_buttons(self):
        self.start_button.config(state=tk.NORMAL)
        self.clear_log_button.config(state=tk.NORMAL)


if __name__ == '__main__':
    # --- For PyInstaller to correctly find Tkinter data files ---
    # This is often needed if you use custom themes or complex Tk widgets,
    # but for basic ttk it might not be strictly necessary.
    # It helps ensure Tcl/Tk files are bundled.
    # if os.environ.get('OS', '').lower().startswith('windows'):
    #     # Add path to tcl/tk library if not found automatically by PyInstaller
    #     # Usually PyInstaller handles this well, but this is a fallback.
    #     # Example (adjust to your Python installation if needed):
    #     py_dir = os.path.dirname(os.sys.executable)
    #     os.environ['TCL_LIBRARY'] = os.path.join(py_dir, 'tcl', 'tcl8.6')
    #     os.environ['TK_LIBRARY'] = os.path.join(py_dir, 'tcl', 'tk8.6')

    # --- Required for Pillow to find its _tkinter_finder module when frozen ---
    # This helps Pillow's Tkinter PhotoImage interface work correctly in the EXE.
    try:
        from PIL import _tkinter_finder
    except ImportError:
        pass  # If not found, Pillow might still work for non-Tk operations

    root = tk.Tk()
    app = MangaConverterApp(root)
    root.mainloop()

    # pyinstaller - -name
    # MangaPDFConverter - -onefile - -windowed ^
    # --add - data
    # "C:\Python39\tcl\tcl8.6;tcl/tcl8.6" ^
    # --add - data
    # "C:\Python39\tcl\tk8.6;tk/tk8.6" ^
    # --icon = your_icon.ico
    # manga_converter_gui_1.py