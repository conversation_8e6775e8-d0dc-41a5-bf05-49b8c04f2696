import openpyxl as openpyxl
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('TkAgg')  # 更改后端为 TkAgg
import random

# 输入数据，通常从 CSV 或 Excel 文件读取。这里手动输入数据。
data = {
    'baidu': {
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [952,1264, 1576, 1877, 2198, 2509, 2820],
        'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224]
    },
    'tencent': {  # 另一个数据组的示例
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [954,1090, 1404, 1717, 2030, 2343, 2656],
        'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224]
    },
    # 'ali': {  # 另一个数据组的示例
    #     '序号': [1, 2, 3, 4, 5, 6, 7],
    #     'sumH3(bytes)': [1300, 1600, 1900, 2200, 2500, 2800, 3100],
    #     'sumH1(kb)': [35, 70, 105, 140, 175, 210, 245]
    # }
}

# 初始化数据
data = {
    'baidu': {
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [952, 1264, 1576, 1877, 2198, 2509, 2820],
        'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224]
    },
    'tencent': {
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [954, 1090, 1404, 1717, 2030, 2343, 2656],
        'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224]
    },
    'ali': {
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [1349, 2353, 3092, 3799, 4574, 5312, 6052],
        'sumH1(kb)': [66, 132, 194, 260, 324, 390, 456]
    }
}

# 目标序号范围
target_seq = list(range(1, 129))  # 1 到 128

# 生成完整数据
for group in data.keys():
    current_length = len(data[group]['序号'])
    for seq in range(current_length + 1, 129):  # 从第8个开始到128
        prev_sumH3 = data[group]['sumH3(bytes)'][-1]
        if group == 'baidu':
            increment = random.choice([311, 312])
        elif group == 'tencent':
            increment = random.choice([313, 314])
        elif group == 'ali':

            increment = random.randint(707, 755)
        new_sumH3 = prev_sumH3 + increment
        new_sumH1 = 32 * seq  # sumH1(kb) 为序号的32倍

        data[group]['序号'].append(seq)
        data[group]['sumH3(bytes)'].append(new_sumH3)
        data[group]['sumH1(kb)'].append(new_sumH1)

data['ali']['sumH1(kb)'] = [66 * i for i in range(1, len(data['ali']['sumH3(bytes)']) + 1)]
print(data['ali']['sumH1(kb)'])
print(data['ali']['sumH3(bytes)'])

# 创建一个字典来存储DataFrame
dfs = {}

# 将每个数据组转换为DataFrame，并计算比率
for group_name, group_data in data.items():
    df = pd.DataFrame(group_data)
    df['sumH1/sumH3'] = df['sumH1(kb)'] * 1024 / df['sumH3(bytes)']  # 转换单位后计算比率
    dfs[group_name] = df

# 设置绘图风格
# plt.style.use('seaborn-darkgrid')
# 设置中文字体和负号显示
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文字体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

fig, axs = plt.subplots(1, 1, figsize=(10, 12))

# 绘制sumH3和sumH1
# for group_name, df in dfs.items():
#     axs[0].plot(df['序号'], df['sumH3(bytes)'], marker='o', label=f'{group_name} sumH3(bytes)')
#     axs[0].plot(df['序号'], df['sumH1(kb)']*1024, marker='s', label=f'{group_name} sumH1(bytes)')  # 转换为bytes以便比较
# axs[0].set_title('sumH3 和 sumH1 序列图')
# axs[0].set_xlabel('序号')
# axs[0].set_ylabel('字节数 (bytes)')
# axs[0].legend()
# axs[0].grid(True)

# 绘制sumH1/sumH3比率
for group_name, df in dfs.items():
    axs.plot(df['序号'], df['sumH1/sumH3'], marker='.', label=f'{group_name} sumH1/sumH3')
axs.set_title('sumH1/sumH3 比率图')
axs.set_xlabel('序号')
axs.set_ylabel('比率 (sumH1/sumH3)')
axs.legend()
axs.grid(True)

plt.tight_layout()
plt.show()



#  128的放大倍率：
# 打印序号128的数据
summary_data = {}
for group_name, df in dfs.items():
    row_128 = df[df['序号'] == 128]
    if not row_128.empty:
        sumH1 = row_128['sumH1(kb)'].values[0]
        sumH3 = row_128['sumH3(bytes)'].values[0]
        ratio = row_128['sumH1/sumH3'].values[0]
        summary_data[group_name] = {
            '序号': 128,
            'sumH1(kb)': sumH1,
            'sumH3(bytes)': sumH3,
            'sumH1/sumH3': ratio
        }

# 创建汇总表格
summary_df = pd.DataFrame(summary_data).T  # 转置以便每个组成为一行
summary_df = summary_df[['序号', 'sumH1(kb)', 'sumH3(bytes)', 'sumH1/sumH3']]
summary_df.to_excel('summary_128.xlsx')


print("序号128的放大倍率（sumH1/sumH3）:")
print(summary_df)