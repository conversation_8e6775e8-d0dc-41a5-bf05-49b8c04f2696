import os
import re
from PIL import Image


def create_manga_pdf(image_folder, output_filename="manga_output.pdf", resolution=150.0):
    """
    将指定文件夹中的图片合并成一个PDF文件。

    参数:
    image_folder (str): 包含图片的文件夹路径。
    output_filename (str): 输出的PDF文件名 (不含路径，将保存在image_folder内)。
    resolution (float): PDF中图像的分辨率 (DPI)。
    """
    # output_pdf_path 将在调用此函数前确定，并作为 output_filename 的一部分或在此构建
    # 为了通用性，我们假设 output_filename 只是文件名，路径由 image_folder 决定
    actual_output_pdf_path = os.path.join(image_folder, output_filename)

    if os.path.exists(actual_output_pdf_path):
        print(f"  PDF已存在，跳过: {actual_output_pdf_path}")
        return True  # 返回True表示已处理或跳过

    # 列出当前文件夹下的图片
    image_files_in_this_folder = []
    supported_formats = ('.jpg', '.jpeg', '.png')
    try:
        for f_name in os.listdir(image_folder):
            if f_name.lower().endswith(supported_formats):
                image_files_in_this_folder.append(os.path.join(image_folder, f_name))
    except FileNotFoundError:
        print(f"  错误：文件夹 '{image_folder}' 在列出文件时未找到。")
        return False
    except Exception as e:
        print(f"  列出文件夹 '{image_folder}' 中的文件时出错: {e}")
        return False

    if not image_files_in_this_folder:
        # print(f"  文件夹 '{image_folder}' 中没有找到支持的图片文件。") # 可能有点吵，递归时很多文件夹没图片
        return False  # 没有图片则不创建

    # 自定义排序函数
    def sort_key(filepath):
        filename = os.path.basename(filepath).lower()
        if filename == "cover.jpg":
            return (-1, "")
        match = re.match(r'i_(\d+)\.(jpg|jpeg|png)', filename)
        if match:
            return (0, int(match.group(1)))
        return (1, filename)

    image_files_in_this_folder.sort(key=sort_key)

    # print(f"  在 '{os.path.basename(image_folder)}' 中找到并排序的图片:")
    # for i, f_path in enumerate(image_files_in_this_folder):
    #     print(f"    {i+1}. {os.path.basename(f_path)}")

    pil_images = []
    opened_images_refs = []

    try:
        for i, f_path in enumerate(image_files_in_this_folder):
            # print(f"    正在处理: {os.path.basename(f_path)} ({i+1}/{len(image_files_in_this_folder)})")
            img = Image.open(f_path)
            opened_images_refs.append(img)

            if img.mode == 'RGBA':
                background = Image.new("RGB", img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[3])
                img_to_add = background
            elif img.mode == 'P':
                img_to_add = img.convert('RGB')
            elif img.mode == 'L':
                img_to_add = img
            elif img.mode == 'RGB':
                img_to_add = img
            else:
                img_to_add = img.convert('RGB')

            pil_images.append(img_to_add)

    except Exception as e:
        print(f"  在 '{os.path.basename(image_folder)}' 中打开或转换图片时出错: {e}")
        for opened_img in opened_images_refs:
            opened_img.close()
        return False

    if not pil_images:
        # print(f"  没有图片可以添加到 '{os.path.basename(image_folder)}' 的PDF。")
        for opened_img in opened_images_refs:
            opened_img.close()
        return False

    first_image_to_save = pil_images[0]
    other_images_to_save = pil_images[1:]

    try:
        print(f"  为 '{os.path.basename(image_folder)}' 创建PDF: {output_filename} (共 {len(pil_images)} 页)")
        first_image_to_save.save(
            actual_output_pdf_path,
            "PDF",
            resolution=resolution,
            save_all=True,
            append_images=other_images_to_save
        )
        print(f"  PDF创建成功: {actual_output_pdf_path}")
        return True
    except Exception as e:
        print(f"  保存PDF '{actual_output_pdf_path}' 时出错: {e}")
        return False
    finally:
        for opened_img in opened_images_refs:
            opened_img.close()


def process_directory_recursively(root_dir, resolution=150.0):
    """
    递归处理指定根目录下的所有子文件夹。
    如果子文件夹直接包含图片，则为其创建一个PDF。
    """
    print(f"开始递归扫描根目录: {root_dir}")
    processed_count = 0
    skipped_count = 0
    failed_count = 0

    for dirpath, dirnames, filenames in os.walk(root_dir):
        # dirpath: 当前正在访问的文件夹路径
        # dirnames: dirpath中子文件夹的列表 (os.walk会进一步访问它们)
        # filenames: dirpath中文件的列表

        # 检查当前dirpath是否直接包含图片文件
        has_images = any(fname.lower().endswith(('.jpg', '.jpeg', '.png')) for fname in filenames)

        if has_images:
            print(f"\n发现包含图片的文件夹: {dirpath}")

            # 使用文件夹名作为PDF名
            folder_basename = os.path.basename(dirpath)
            if not folder_basename:  # 如果路径以/或\结尾，basename可能是空的
                # 这通常不会在os.walk的dirpath中发生，但以防万一
                folder_basename = os.path.basename(os.path.dirname(dirpath.rstrip(os.sep)))

            output_pdf_name = f"{folder_basename}.pdf"
            output_pdf_name = re.sub(r'[\\/*?:"<>|]', "_", output_pdf_name)  # 清理非法字符

            # 调用PDF创建函数
            # PDF将保存在 dirpath 内部
            result = create_manga_pdf(dirpath, output_pdf_name, resolution=resolution)
            if result is True:  # PDF创建成功或已存在并跳过
                # 如果函数返回True但PDF已存在，它会打印跳过信息
                # 如果是真的创建了，它也会打印成功信息
                # 为了计数，我们假设“跳过”也是一种成功处理
                if os.path.exists(os.path.join(dirpath, output_pdf_name)):  # 再次确认
                    # 如果是跳过，create_manga_pdf内部会打印
                    # 如果是新创建，create_manga_pdf内部也会打印
                    # 此处主要是为了计数
                    if "PDF已存在" not in open(os.devnull, 'w').__repr__():  # 避免重复打印的技巧
                        pass  # create_manga_pdf 已经打印了
                    processed_count += 1
                else:  # 应该不会到这里，除非 create_manga_pdf 返回True但没创建
                    failed_count += 1

            elif result is False:  # 创建失败或没有图片
                # create_manga_pdf 内部会打印错误或无图片信息
                # 如果是因为没有图片，它返回False，但不算是一个“失败”
                # 我们只在create_manga_pdf尝试创建但失败时计数
                # create_manga_pdf内部的逻辑现在是：没有图片直接返回False，不打印。
                # 如果我们想区分“无图片”和“创建失败”，create_manga_pdf需要返回不同值。
                # 目前简单处理：如果create_manga_pdf返回False，且它确实尝试了（即有图片），则算失败。
                # 但create_manga_pdf现在如果没图片就直接返回False了。
                # 假设create_manga_pdf出错会打印，这里只计数
                is_actually_failed = False
                try:
                    # 重新检查是否有图片，因为create_manga_pdf可能因为其他原因失败
                    if any(fname.lower().endswith(('.jpg', '.jpeg', '.png')) for fname in os.listdir(dirpath)):
                        is_actually_failed = True  # 有图片但创建失败
                except:
                    pass  # 忽略listdir错误，前面已经处理过了

                if is_actually_failed:
                    failed_count += 1
                else:
                    skipped_count += 1  # 可能是因为没有有效图片被处理

        # else:
        # print(f"  文件夹 {dirpath} 不直接包含图片，跳过PDF创建 (但会继续扫描其子文件夹)。")
        # pass # os.walk会自动处理子文件夹

    print(f"\n--- 扫描完成 ---")
    print(f"成功处理/跳过已存在: {processed_count} 个文件夹")
    print(f"因无图片等原因跳过: {skipped_count} 个文件夹的PDF创建尝试")
    print(f"创建失败: {failed_count} 个文件夹")


if __name__ == "__main__":
    # root_directory = input("请输入要递归扫描的根文件夹路径: ")
    # 例如: E:\Comics\C1494-[ほおぶくろっ! (ベコ太郎)]
    # 或 E:\Comics\C1494-[ほおぶくろっ! (ベコ太郎)]\2. 商业志

    # 为了测试，你可以硬编码路径
    # root_directory = r"D:\path\to\your\C1494-[ほおぶくろっ! (ベコ太郎)]"
    root_directory = input("请输入要递归扫描的根文件夹路径: ").strip()

    if not os.path.isdir(root_directory):
        print(f"错误：提供的路径 '{root_directory}' 不是一个有效的文件夹。")
    else:
        try:
            user_resolution = float(input("请输入PDF的分辨率 (例如 150, 默认150): ") or "150.0")
        except ValueError:
            print("无效的分辨率输入，使用默认值 150 DPI。")
            user_resolution = 150.0

        process_directory_recursively(root_directory, resolution=user_resolution)