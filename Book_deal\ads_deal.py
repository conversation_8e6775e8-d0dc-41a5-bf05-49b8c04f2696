import os


def rename_files_and_dirs(root_dir, old_str, new_str):
    # 遍历根目录，自底向上处理以防止路径错误
    for root, dirs, files in os.walk(root_dir, topdown=False):
        # 先处理文件
        for name in files:
            if old_str in name:
                new_name = name.replace(old_str, new_str)
                src_path = os.path.join(root, name)
                dst_path = os.path.join(root, new_name)

                try:
                    os.rename(src_path, dst_path)
                    print(f"文件重命名成功：'{src_path}' -> '{dst_path}'")
                except Exception as e:
                    print(f"处理文件时出错：{e}")

        # 后处理目录
        for name in dirs:
            if old_str in name:
                new_name = name.replace(old_str, new_str)
                src_path = os.path.join(root, name)
                dst_path = os.path.join(root, new_name)

                try:
                    os.rename(src_path, dst_path)
                    print(f"目录重命名成功：'{src_path}' -> '{dst_path}'")
                except Exception as e:
                    print(f"处理目录时出错：{e}")


if __name__ == "__main__":
    # 配置参数
    target_dir = r"C:\Users\<USER>\Documents\阿衰 全集漫画+古典珍藏版 PDF格式"
    old_string = "【更多精彩资源尽在 郢书燕说】"
    new_string = ""  # 替换为单个空格

    # 执行重命名操作
    rename_files_and_dirs(target_dir, old_string, new_string)
    print("所有文件处理完成，请检查输出信息确认是否有未处理的异常。")