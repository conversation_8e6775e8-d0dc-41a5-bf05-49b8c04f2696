import openpyxl as openpyxl
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('TkAgg')  # 更改后端为 TkAgg
import random


# 让每一个条目 尽可能大，比如 Tencent：  headers = [('a' * 22, "e" * 1365)] * 23

# 初始化数据
data = {
    'baidu': {
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [952, 1264, 1576, 1877, 2198, 2509, 2820],
        'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224]
    },
'baidu': {'序号': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
                    27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
                    53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,
                    79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103,
                    104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128],
           'sumH3(bytes)': [752, 895, 1046, 1189, 1333, 1476, 1619, 1763, 1907, 2050, 2193, 2336, 2480, 2623, 2766, 2910, 3053, 3196, 3339, 3482, 3625, 3768, 3912, 4056, 4200, 4343, 4487, 4631, 4774, 4918, 5061, 5205, 5349, 5492, 5635, 5779, 5922, 6065, 6208, 6351, 6494, 6637, 6781, 6924, 7067, 7211, 7354, 7497, 7640, 7784, 7927, 8071, 8214, 8357, 8501, 8644, 8788, 8931, 9074, 9217, 9360, 9503, 9647, 9791, 9935, 10078, 10222, 10366, 10509, 10652, 10795, 10939, 11083, 11226, 11369, 11513, 11656, 11800, 11943, 12086, 12230, 12374, 12518, 12662, 12806, 12950, 13093, 13236, 13379, 13522, 13666, 13810, 13954, 14098, 14242, 14385, 14528, 14671, 14814, 14958, 15102, 15245, 15388, 15531, 15675, 15818, 15962, 16106, 16250, 16394, 16538, 16682, 16826, 16970, 17113, 17256, 17399, 17542, 17685, 17829, 17972, 18116, 18259, 18402, 18545, 18689, 18833, 18977],

           'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224, 256, 288, 320, 352, 384, 416, 448, 480, 512, 544, 576, 608, 640, 672, 704, 736, 768, 800, 832, 864, 896, 928, 960, 992, 1024, 1056, 1088, 1120, 1152, 1184, 1216, 1248, 1280, 1312, 1344, 1376, 1408, 1440, 1472, 1504, 1536, 1568, 1600, 1632, 1664, 1696, 1728, 1760, 1792, 1824, 1856, 1888, 1920, 1952, 1984, 2016, 2048, 2080, 2112, 2144, 2176, 2208, 2240, 2272, 2304, 2336, 2368, 2400, 2432, 2464, 2496, 2528, 2560, 2592, 2624, 2656, 2688, 2720, 2752, 2784, 2816, 2848, 2880, 2912, 2944, 2976, 3008, 3040, 3072, 3104, 3136, 3168, 3200, 3232, 3264, 3296, 3328, 3360, 3392, 3424, 3456, 3488, 3520, 3552, 3584, 3616, 3648, 3680, 3712, 3744, 3776, 3808, 3840, 3872, 3904, 3936, 3968, 4000, 4032, 4064, 4096]},


    'tencent': {  # 另一个数据组的示例
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [740, 880, 1013, 1146, 1286, 1419, 1560],
        'sumH1(kb)': [32, 64, 96, 128, 160, 192, 224]
    },
    'ali': {
        '序号': [1, 2, 3, 4, 5, 6, 7],
        'sumH3(bytes)': [1153, 1352, 1520, 1687, 1855, 2023, 2190],
        'sumH1(kb)': [69, 138, 207, 276, 345, 414, 483]
    }
}

# 目标序号范围
target_seq = list(range(1, 129))  # 1 到 128

# 生成完整数据
for group in data.keys():
    current_length = len(data[group]['序号'])
    for seq in range(current_length + 1, 129):  # 从第8个开始到128
        prev_sumH3 = data[group]['sumH3(bytes)'][-1]

        if group == 'tencent':
            increment = random.randint(133, 141)
        # elif group == 'baidu':
        #     increment = random.choice([311, 312])
        elif group == 'ali':

            increment = random.randint(167, 168)
        new_sumH3 = prev_sumH3 + increment
        new_sumH1 = 32 * seq  # sumH1(kb) 为序号的32倍

        data[group]['序号'].append(seq)
        data[group]['sumH3(bytes)'].append(new_sumH3)
        data[group]['sumH1(kb)'].append(new_sumH1)

data['ali']['sumH1(kb)'] = [69 * i for i in range(1, len(data['ali']['sumH3(bytes)']) + 1)]
print(data['ali']['sumH1(kb)'])
print(data['ali']['sumH3(bytes)'])

# 创建一个字典来存储DataFrame
dfs = {}

# 将每个数据组转换为DataFrame，并计算比率
for group_name, group_data in data.items():
    df = pd.DataFrame(group_data)
    df['sumH1/sumH3'] = df['sumH1(kb)'] * 1024 / df['sumH3(bytes)']  # 转换单位后计算比率
    dfs[group_name] = df

# ...（前面数据准备部分的代码保持不变）

# 设置绘图风格和参数
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

fig, axs = plt.subplots(1, 1, figsize=(14, 8))  # 适当增加宽度以容纳更长的x轴

# 绘制sumH1/sumH3比率
for group_name, df in dfs.items():
    axs.plot(df['序号'], df['sumH1/sumH3'], marker='.', label=f'{group_name} sumH1/sumH3')

# 设置x轴范围到256
axs.set_xlim(0, 256)  # 强制x轴显示到256
axs.set_xticks(range(0, 401, 32))  # 每32个单位显示一个刻度（0,32,64,...,256）

# 添加辅助网格线
axs.grid(True, which='both', linestyle='--', linewidth=0.5)

axs.set_title('sumH1/sumH3 比率图（x轴扩展至256）')
axs.set_xlabel('序号')
axs.set_ylabel('比率 (sumH1/sumH3)')
axs.legend()

plt.tight_layout()
plt.show()

