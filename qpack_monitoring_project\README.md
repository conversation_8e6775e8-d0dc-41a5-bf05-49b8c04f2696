# QPACK 动态表压缩监控项目

## 📋 项目简介

本项目是一个专业的 HTTP/3 QPACK 动态表压缩效果监控和分析工具。通过实时监控 QPACK 编码过程，提供详细的压缩统计数据和可视化分析，帮助验证和量化 QPACK 动态表压缩的实际效果。

## 🎯 主要功能

- **实时压缩监控**：监控每次 HTTP/3 请求的 QPACK 编码过程
- **动态表分析**：分析动态表的建立、使用和优化过程
- **压缩效果量化**：提供详细的压缩比率、节省字节数等统计数据
- **阶段性分析**：区分动态表建立期、优化期、稳定期的不同表现
- **可视化报告**：生成专业的图表和报告
- **数据导出**：支持 JSON 格式的详细数据导出

## 📁 文件结构

```
qpack_monitoring_project/
├── h3_client_qpack_v1_2_ali.py     # 主程序：带监控功能的 HTTP/3 客户端
├── analyze_qpack_results.py        # 数据分析脚本
├── visualize_qpack_results.py      # 可视化图表生成脚本
├── qpack_stats_*.json              # 测试数据文件
├── qpack_*.png                     # 生成的图表文件
└── README.md                       # 项目说明文档
```

## 🚀 快速开始

### 1. 环境要求

```bash
# Python 依赖
pip install aioquic matplotlib numpy

# 系统要求
- Python 3.8+
- Windows/Linux/macOS
- 网络连接（用于 HTTP/3 测试）
```

### 2. 运行主程序

```bash
# 进入项目目录
cd qpack_monitoring_project

# 运行 QPACK 监控测试
python h3_client_qpack_v1_2_ali.py

# 可选参数
python h3_client_qpack_v1_2_ali.py --verbose  # 详细日志
python h3_client_qpack_v1_2_ali.py --help     # 查看所有参数
```

### 3. 分析结果

```bash
# 运行数据分析
python analyze_qpack_results.py

# 生成可视化图表
python visualize_qpack_results.py
```

## 📊 输出说明

### 实时监控输出

程序运行时会显示每次请求的详细信息：

```
📊 请求 #2 (Stream 4) QPACK 编码分析:
   原始头部大小: 646 字节
   压缩后大小: 57 字节
   编码器更新: 160 字节
   压缩比率: 91.18%
   节省字节: 589 字节
```

### 生成的文件

1. **qpack_stats_*.json** - 详细的测试数据
2. **qpack_compression_trends.png** - 压缩趋势图
3. **qpack_phase_comparison.png** - 阶段对比图
4. **qpack_efficiency_analysis.png** - 效率分析图
5. **qpack_summary_report.png** - 总结报告图

## 🔍 关键指标解释

### 压缩比率
- **66-70%**：基础压缩效果（静态表 + Huffman 编码）
- **90%+**：动态表生效的高压缩比
- **稳定期**：动态表建立后的持续压缩效果

### 编码器更新
- **160字节**：大量动态表条目建立
- **12字节**：微调优化
- **0字节**：使用已建立的动态表

### 动态表阶段
1. **建立期**（请求1-5）：动态表逐步建立，压缩比快速提升
2. **优化期**（请求6-20）：动态表优化，压缩比稳定
3. **稳定期**（请求21+）：动态表成熟，压缩效果一致

## 🛠️ 自定义配置

### 修改测试参数

在 `h3_client_qpack_v1_2_ali.py` 中可以调整：

```python
# 请求数量
num_requests = 50

# 目标服务器
base_url = "https://aliyun.hawks.top"

# 测试头部
headers = [
    ('X-Custom-App', 'QPACK-Test-Application'),
    ('X-Session-ID', 'session-12345'),
    # ... 更多头部
]
```

### 修改分析参数

在 `analyze_qpack_results.py` 中可以调整分析阈值和分组条件。

## 📈 测试结果示例

基于实际测试数据：

- **总体压缩比**：70.66%
- **总节省字节**：35,937字节（50个请求）
- **动态表生效时间**：第2个请求
- **稳定期一致性**：标准差0.06%（优秀）

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认目标服务器支持 HTTP/3

2. **依赖缺失**
   ```bash
   pip install aioquic matplotlib numpy
   ```

3. **权限问题**
   - 确保有文件写入权限
   - 检查防火墙设置

### 调试模式

```bash
python h3_client_qpack_v1_2_ali.py --verbose
```

## 📝 技术原理

### QPACK 动态表工作原理

1. **静态表**：预定义的常用头部字段
2. **动态表**：运行时建立的头部字段索引
3. **索引引用**：使用索引号代替完整头部内容
4. **Huffman 编码**：进一步压缩字符串内容

### 监控实现原理

1. **继承 H3Connection**：重写 `_encode_headers` 方法
2. **实时统计**：记录编码前后的大小变化
3. **动态表推断**：通过压缩比变化和编码器活动分析
4. **数据收集**：完整记录每次编码的详细信息

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📄 许可证

本项目遵循 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过 GitHub Issues 联系。

---

**注意**：本工具仅用于学习和研究目的，请确保在合法合规的环境中使用。
